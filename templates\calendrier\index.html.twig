{% extends 'base.html.twig' %}

{% block title %}Calendrier - OSI Manager{% endblock %}

{% block stylesheets %}
    {{ parent() }}
    <link href='https://cdn.jsdelivr.net/npm/fullcalendar@6.1.10/index.global.min.css' rel='stylesheet' />
    <style>
        /* S'assurer que le modal passe au-dessus de FullCalendar */
        .fc {
            z-index: 1;
        }
        #addSegmentModal {
            z-index: 9999 !important;
        }
        #addSegmentModal > div {
            z-index: 10000 !important;
        }

        /* Styles pour le calendrier */
        #calendar {
            min-height: 600px;
        }
    </style>
{% endblock %}

{% block body %}
<div class="px-4 py-6 sm:px-0">
    <!-- En-tête -->
    <div class="sm:flex sm:items-center mb-8">
        <div class="sm:flex-auto">
            <h1 class="text-3xl font-bold text-gray-900">Calendrier des missions</h1>
            <p class="mt-2 text-gray-600">Gestion des segments de mission</p>
        </div>
        <div class="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
            <button type="button" onclick="openSegmentModal()" class="inline-flex items-center justify-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 sm:w-auto">
                Ajouter un segment
            </button>
        </div>
    </div>

    <!-- Légende -->
    <div class="bg-white shadow rounded-lg mb-6">
        <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Légende</h3>
            <div class="flex flex-wrap gap-4">
                <div class="flex items-center">
                    <div class="w-4 h-4 bg-blue-500 rounded mr-2"></div>
                    <span class="text-sm text-gray-700">Voyage</span>
                </div>
                <div class="flex items-center">
                    <div class="w-4 h-4 bg-green-500 rounded mr-2"></div>
                    <span class="text-sm text-gray-700">Intervention</span>
                </div>
                <div class="flex items-center">
                    <div class="w-4 h-4 bg-orange-500 rounded mr-2"></div>
                    <span class="text-sm text-gray-700">Stand-by</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Calendrier -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <div id="calendar"></div>
        </div>
    </div>
</div>

<!-- Modal d'ajout/modification de segment -->
<div id="addSegmentModal" class="fixed inset-0 bg-gray-600 bg-opacity-75 overflow-y-auto h-full w-full hidden" style="z-index: 9999;">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white" style="z-index: 10000;">
        <div class="mt-3">
            <h3 class="text-lg font-medium text-gray-900 mb-4" id="modalTitle">Ajouter un segment</h3>
            <form id="segmentForm">
                <input type="hidden" id="segmentId" name="segmentId">
                <div class="space-y-4">
                    <div>
                        <label for="userSelect" class="block text-sm font-medium text-gray-700">Utilisateur *</label>
                        <select id="userSelect" name="userId" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                            <option value="">Sélectionner un utilisateur</option>
                        </select>
                    </div>
                    <div>
                        <label for="missionSelect" class="block text-sm font-medium text-gray-700">Mission *</label>
                        <select id="missionSelect" name="missionId" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                            <option value="">Sélectionner une mission</option>
                        </select>
                    </div>
                    <div>
                        <label for="typeSelect" class="block text-sm font-medium text-gray-700">Type</label>
                        <select id="typeSelect" name="type" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                            <option value="">Sélectionner un type</option>
                            <option value="VOYAGE">Voyage</option>
                            <option value="INTERVENTION">Intervention</option>
                            <option value="STAND_BY">Stand-by</option>
                        </select>
                    </div>
                    <div>
                        <label for="dateHeureDebut" class="block text-sm font-medium text-gray-700">Date et heure de début</label>
                        <input type="datetime-local" id="dateHeureDebut" name="dateHeureDebut" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                    </div>
                    <div>
                        <label for="dateHeureFin" class="block text-sm font-medium text-gray-700">Date et heure de fin</label>
                        <input type="datetime-local" id="dateHeureFin" name="dateHeureFin" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                    </div>
                </div>
                <div class="flex justify-end space-x-3 mt-6">
                    <button type="button" onclick="closeModal('addSegmentModal')" class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200">
                        Annuler
                    </button>
                    <button type="button" id="deleteButton" onclick="deleteSegment()" class="px-4 py-2 text-sm font-medium text-white bg-red-600 rounded-md hover:bg-red-700 hidden">
                        Supprimer
                    </button>
                    <button type="submit" class="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700">
                        Enregistrer
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script src='https://cdn.jsdelivr.net/npm/fullcalendar@6.1.10/index.global.min.js'></script>
    <script>
        let calendar;
        let currentSegmentId = null;
        let allUsers = [];
        let allMissions = [];

        // URLs pour les appels API
        const apiUrls = {
            segments: "{{ path('api_segment_index') }}",
            missions: "{{ path('api_mission_index') }}",
            users: "{{ path('api_user_index') }}",
            segmentUpdate: "{{ path('api_segment_update', {id: '__ID__'}) }}".replace('__ID__', ''),
            segmentDelete: "{{ path('api_segment_delete', {id: '__ID__'}) }}".replace('__ID__', ''),
            segmentCreate: "{{ path('api_segment_create') }}"
        };

        // Charger les utilisateurs
        async function loadUsers() {
            try {
                console.log('Chargement des utilisateurs...');
                const response = await ajax.get(apiUrls.users);
                allUsers = response.data.data || response.data || [];
                console.log('Utilisateurs chargés:', allUsers);

                const userSelect = document.getElementById('userSelect');
                userSelect.innerHTML = '<option value="">Sélectionner un utilisateur</option>';

                allUsers.forEach(user => {
                    const option = document.createElement('option');
                    option.value = user.id;
                    option.textContent = `${user.prenom} ${user.nom} (${user.role})`;
                    userSelect.appendChild(option);
                });
            } catch (error) {
                console.error('Erreur lors du chargement des utilisateurs:', error);
            }
        }

        // Charger les missions
        async function loadMissions() {
            try {
                console.log('Chargement des missions...');
                const response = await ajax.get(apiUrls.missions);
                console.log('Réponse brute API missions:', response);

                // Essayer différents formats de réponse
                if (response.data && Array.isArray(response.data)) {
                    allMissions = response.data;
                } else if (response.data && response.data.data && Array.isArray(response.data.data)) {
                    allMissions = response.data.data;
                } else if (Array.isArray(response)) {
                    allMissions = response;
                } else {
                    console.error('Format de réponse missions inattendu:', response);
                    allMissions = [];
                }

                console.log('Missions chargées:', allMissions);
                console.log('Nombre de missions:', allMissions.length);

                // Afficher toutes les missions au départ
                populateAllMissions();
            } catch (error) {
                console.error('Erreur lors du chargement des missions:', error);
            }
        }

        // Remplir le sélecteur avec toutes les missions
        function populateAllMissions() {
            const missionSelect = document.getElementById('missionSelect');
            missionSelect.innerHTML = '<option value="">Sélectionner une mission</option>';

            allMissions.forEach(mission => {
                const option = document.createElement('option');
                option.value = mission.id;

                // Afficher les utilisateurs assignés
                let assignedUsers = '';
                if (mission.users && mission.users.length > 0) {
                    assignedUsers = mission.users.map(u => `${u.prenom} ${u.nom}`).join(', ');
                } else {
                    assignedUsers = 'Non assigné';
                }

                option.textContent = `${mission.titre} (${mission.pays}) - ${assignedUsers}`;
                missionSelect.appendChild(option);
            });
        }

        // Filtrer les missions par utilisateur
        function filterMissionsByUser(userId) {
            const missionSelect = document.getElementById('missionSelect');
            missionSelect.innerHTML = '<option value="">Sélectionner une mission</option>';

            if (!userId) {
                // Si aucun utilisateur sélectionné, afficher toutes les missions
                populateAllMissions();
                return;
            }

            const userMissions = allMissions.filter(mission => {
                // Vérifier si l'utilisateur est assigné à cette mission
                return mission.users && mission.users.some(user => user.id == userId);
            });

            console.log(`Missions filtrées pour l'utilisateur ${userId}:`, userMissions);

            if (userMissions.length === 0) {
                const option = document.createElement('option');
                option.value = '';
                option.textContent = 'Aucune mission assignée à cet utilisateur';
                option.disabled = true;
                missionSelect.appendChild(option);
                return;
            }

            userMissions.forEach(mission => {
                const option = document.createElement('option');
                option.value = mission.id;
                option.textContent = `${mission.titre} (${mission.pays})`;
                missionSelect.appendChild(option);
            });
        }

        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM chargé, initialisation du calendrier...');

            // Charger les données au démarrage
            loadUsers();
            loadMissions();

            // Event listener pour le changement d'utilisateur
            document.getElementById('userSelect').addEventListener('change', function() {
                const userId = this.value;
                console.log('Utilisateur sélectionné:', userId);
                filterMissionsByUser(userId);
            });

            const calendarEl = document.getElementById('calendar');
            console.log('Élément calendrier trouvé:', calendarEl);
            console.log('FullCalendar disponible:', typeof FullCalendar);

            if (!calendarEl) {
                console.error('Élément calendrier non trouvé !');
                return;
            }

            if (typeof FullCalendar === 'undefined') {
                console.error('FullCalendar non chargé !');
                return;
            }

            calendar = new FullCalendar.Calendar(calendarEl, {
                initialView: 'timeGridWeek',
                locale: 'fr',
                timeZone: 'local', // Utiliser le fuseau horaire local du navigateur
                headerToolbar: {
                    left: 'prev,next today',
                    center: 'title',
                    right: 'dayGridMonth,timeGridWeek,timeGridDay'
                },
                height: 'auto',
                slotMinTime: '06:00:00',
                slotMaxTime: '22:00:00',
                businessHours: [
                    {
                        daysOfWeek: [1, 2, 3, 4, 5],
                        startTime: '09:00',
                        endTime: '12:00'
                    },
                    {
                        daysOfWeek: [1, 2, 3, 4, 5],
                        startTime: '14:00',
                        endTime: '18:00'
                    }
                ],
                selectable: true,
                selectMirror: true,
                editable: true,
                eventResizableFromStart: true,
                events: function(info, successCallback, failureCallback) {
                    const url = "{{ path('api_segment_calendar') }}" + '?start=' + encodeURIComponent(info.startStr) + '&end=' + encodeURIComponent(info.endStr);
                    console.log('Chargement des événements depuis:', url);
                    ajax.get(url)
                    .then(response => {
                        console.log('Événements reçus:', response);
                        // L'API retourne directement le tableau d'événements dans response.data
                        successCallback(response.data);
                    })
                    .catch(error => {
                        console.error('Erreur lors du chargement des événements:', error);
                        failureCallback(error);
                    });
                },
                select: function(info) {
                    openSegmentModal();
                    document.getElementById('dateHeureDebut').value = info.startStr.slice(0, 16);
                    document.getElementById('dateHeureFin').value = info.endStr.slice(0, 16);
                },
                eventClick: function(info) {
                    editSegment(info.event);
                },
                eventDrop: function(info) {
                    updateSegmentDates(info.event);
                },
                eventResize: function(info) {
                    updateSegmentDates(info.event);
                }
            });

            calendar.render();
            console.log('Calendrier rendu');
        });

        function openModal(modalId) {
            console.log('openModal appelée avec:', modalId);
            const modal = document.getElementById(modalId);
            console.log('Modal trouvé:', modal);
            if (modal) {
                modal.classList.remove('hidden');
                console.log('Modal ouvert');
            } else {
                console.error('Modal non trouvé:', modalId);
            }
        }

        function closeModal(modalId) {
            document.getElementById(modalId).classList.add('hidden');
            resetForm();
        }

        function openSegmentModal() {
            console.log('openSegmentModal appelée');
            currentSegmentId = null;
            document.getElementById('modalTitle').textContent = 'Ajouter un segment';
            document.getElementById('deleteButton').classList.add('hidden');
            openModal('addSegmentModal');
        }

        function editSegment(event) {
            currentSegmentId = event.id;
            document.getElementById('modalTitle').textContent = 'Modifier le segment';
            document.getElementById('deleteButton').classList.remove('hidden');

            // Remplir le formulaire avec les données de l'événement
            document.getElementById('segmentId').value = event.id;
            document.getElementById('typeSelect').value = event.extendedProps.type;

            // Sélectionner l'utilisateur et filtrer les missions
            if (event.extendedProps.userId) {
                document.getElementById('userSelect').value = event.extendedProps.userId;
                filterMissionsByUser(event.extendedProps.userId);
            }

            // Sélectionner la mission
            if (event.extendedProps.missionId) {
                // Attendre un peu que les missions soient filtrées
                setTimeout(() => {
                    document.getElementById('missionSelect').value = event.extendedProps.missionId;
                }, 100);
            }

            // Convertir les dates ISO en format datetime-local
            const dateDebut = new Date(event.start);
            const dateFin = new Date(event.end);

            // Formater pour datetime-local (YYYY-MM-DDTHH:mm)
            document.getElementById('dateHeureDebut').value = formatDateTimeLocal(dateDebut);
            document.getElementById('dateHeureFin').value = formatDateTimeLocal(dateFin);

            openModal('addSegmentModal');
        }

        function formatDateTimeLocal(date) {
            // Créer une nouvelle date en heure locale
            const localDate = new Date(date.getTime() - (date.getTimezoneOffset() * 60000));
            const year = localDate.getFullYear();
            const month = String(localDate.getMonth() + 1).padStart(2, '0');
            const day = String(localDate.getDate()).padStart(2, '0');
            const hours = String(localDate.getHours()).padStart(2, '0');
            const minutes = String(localDate.getMinutes()).padStart(2, '0');

            return `${year}-${month}-${day}T${hours}:${minutes}`;
        }

        function resetForm() {
            document.getElementById('segmentForm').reset();
            currentSegmentId = null;
        }



        async function updateSegmentDates(event) {
            try {
                // Convertir les dates FullCalendar en UTC
                const dateDebut = new Date(event.start);
                const dateFin = new Date(event.end);

                await ajax.put(apiUrls.segmentUpdate + event.id, {
                    dateHeureDebut: dateDebut.toISOString(),
                    dateHeureFin: dateFin.toISOString()
                });
            } catch (error) {
                console.error('Erreur lors de la mise à jour:', error);

                let errorMessage = 'Erreur lors de la mise à jour du segment';

                if (error.data && error.data.errors) {
                    if (Array.isArray(error.data.errors)) {
                        errorMessage = error.data.errors.join('\n');
                    } else {
                        errorMessage = error.data.errors;
                    }
                }

                alert(errorMessage);
                event.revert();
            }
        }

        async function deleteSegment() {
            if (!currentSegmentId) return;

            if (confirm('Êtes-vous sûr de vouloir supprimer ce segment ?')) {
                try {
                    await ajax.delete(apiUrls.segmentDelete + currentSegmentId);
                    calendar.refetchEvents();
                    closeModal('addSegmentModal');
                } catch (error) {
                    console.error('Erreur lors de la suppression:', error);
                    alert('Erreur lors de la suppression du segment');
                }
            }
        }

        // Gestion du formulaire
        document.getElementById('segmentForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const formData = new FormData(e.target);

            // Convertir les dates locales en UTC pour le serveur
            const dateDebut = new Date(formData.get('dateHeureDebut'));
            const dateFin = new Date(formData.get('dateHeureFin'));

            const data = {
                userId: parseInt(formData.get('userId')),
                missionId: parseInt(formData.get('missionId')),
                type: formData.get('type'),
                dateHeureDebut: dateDebut.toISOString(),
                dateHeureFin: dateFin.toISOString()
            };

            try {
                if (currentSegmentId) {
                    await ajax.put(apiUrls.segmentUpdate + currentSegmentId, data);
                } else {
                    await ajax.post(apiUrls.segmentCreate, data);
                }

                calendar.refetchEvents();
                closeModal('addSegmentModal');
            } catch (error) {
                console.error('Erreur lors de l\'enregistrement:', error);

                let errorMessage = 'Erreur lors de l\'enregistrement du segment';

                if (error.data && error.data.errors) {
                    if (Array.isArray(error.data.errors)) {
                        errorMessage = error.data.errors.join('\n');
                    } else {
                        errorMessage = error.data.errors;
                    }
                }

                alert(errorMessage);
            }
        });
    </script>
{% endblock %}

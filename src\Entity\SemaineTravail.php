<?php

namespace App\Entity;

use App\Repository\SemaineTravailRepository;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Serializer\Annotation\Groups;

#[ORM\Entity(repositoryClass: SemaineTravailRepository::class)]
#[ORM\UniqueConstraint(name: 'collaborateur_semaine_unique', columns: ['collaborateur_id', 'semaine_annee'])]
class SemaineTravail
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    #[Groups(['semaine_travail:read'])]
    private ?int $id = null;

    #[ORM\ManyToOne(inversedBy: 'semainesTravail')]
    #[ORM\JoinColumn(nullable: false)]
    #[Groups(['semaine_travail:read', 'semaine_travail:write'])]
    private ?Collaborateur $collaborateur = null;

    #[ORM\Column(length: 7)]
    #[Assert\NotBlank]
    #[Assert\Regex(pattern: '/^\d{4}-\d{2}$/', message: 'Le format doit être YYYY-WW (ex: 2024-01)')]
    #[Groups(['semaine_travail:read', 'semaine_travail:write'])]
    private ?string $semaineAnnee = null;

    #[ORM\Column]
    #[Assert\NotNull]
    #[Assert\Range(min: 0, max: 168)]
    #[Groups(['semaine_travail:read', 'semaine_travail:write'])]
    private ?float $heuresSaisies = null;

    #[ORM\Column(length: 20, nullable: true)]
    #[Groups(['semaine_travail:read'])]
    private ?string $source = 'manuelle';

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getCollaborateur(): ?Collaborateur
    {
        return $this->collaborateur;
    }

    public function setCollaborateur(?Collaborateur $collaborateur): static
    {
        $this->collaborateur = $collaborateur;
        return $this;
    }

    public function getSemaineAnnee(): ?string
    {
        return $this->semaineAnnee;
    }

    public function setSemaineAnnee(string $semaineAnnee): static
    {
        $this->semaineAnnee = $semaineAnnee;
        return $this;
    }

    public function getHeuresSaisies(): ?float
    {
        return $this->heuresSaisies;
    }

    public function setHeuresSaisies(float $heuresSaisies): static
    {
        $this->heuresSaisies = $heuresSaisies;
        return $this;
    }

    public function getAnnee(): int
    {
        return (int) substr($this->semaineAnnee, 0, 4);
    }

    public function getSemaine(): int
    {
        return (int) substr($this->semaineAnnee, 5, 2);
    }

    public function getDateDebutSemaine(): \DateTimeInterface
    {
        $date = new \DateTime();
        $date->setISODate($this->getAnnee(), $this->getSemaine());
        return $date;
    }

    public function getDateFinSemaine(): \DateTimeInterface
    {
        $date = $this->getDateDebutSemaine();
        $date->modify('+6 days');
        return $date;
    }

    public function getHeuresSupplementaires(): float
    {
        if (!$this->collaborateur) {
            return 0;
        }

        $horaireHebdo = $this->collaborateur->getHoraireHebdo();
        return max(0, $this->heuresSaisies - $horaireHebdo);
    }

    public function getHeuresNormales(): float
    {
        if (!$this->collaborateur) {
            return $this->heuresSaisies;
        }

        $horaireHebdo = $this->collaborateur->getHoraireHebdo();
        return min($this->heuresSaisies, $horaireHebdo);
    }

    public static function formatSemaineAnnee(\DateTimeInterface $date): string
    {
        return $date->format('Y-W');
    }

    public static function getSemaineActuelle(): string
    {
        return (new \DateTime())->format('Y-W');
    }

    public function getSource(): ?string
    {
        return $this->source;
    }

    public function setSource(?string $source): static
    {
        $this->source = $source;
        return $this;
    }

    public function isCalculeeAutomatiquement(): bool
    {
        return $this->source === 'automatique';
    }

    public function isSaisieManuelle(): bool
    {
        return $this->source === 'manuelle';
    }

    public function __toString(): string
    {
        return sprintf(
            'Semaine %s - %s (%.1fh)',
            $this->semaineAnnee,
            $this->collaborateur?->getNomComplet() ?? 'Collaborateur inconnu',
            $this->heuresSaisies
        );
    }
}

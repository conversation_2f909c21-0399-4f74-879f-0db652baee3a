<!DOCTYPE html>
<html lang="fr">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>{% block title %}OSI Manager{% endblock %}</title>
        <link rel="icon" href="data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 128 128%22><text y=%221.2em%22 font-size=%2296%22>🌍</text></svg>">

        {% block stylesheets %}
            <script src="https://cdn.tailwindcss.com"></script>
            <script>
                tailwind.config = {
                    theme: {
                        extend: {
                            colors: {
                                primary: {
                                    50: '#eff6ff',
                                    500: '#3b82f6',
                                    600: '#2563eb',
                                    700: '#1d4ed8',
                                }
                            }
                        }
                    }
                }
            </script>
        {% endblock %}
    </head>
    <body class="bg-gray-50">
        <!-- Navigation -->
        <nav class="bg-white shadow-sm border-b border-gray-200">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between h-16">
                    <div class="flex">
                        <div class="flex-shrink-0 flex items-center">
                            <a href="{{ path('app_dashboard') }}" class="text-xl font-bold text-gray-900">
                                🌍 OSI Manager
                            </a>
                        </div>
                        <div class="hidden sm:ml-6 sm:flex sm:space-x-8">
                            <a href="{{ path('app_dashboard') }}" class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                                Dashboard
                            </a>
                            <a href="{{ path('app_users') }}" class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                                Utilisateurs
                            </a>
                            <a href="{{ path('app_missions') }}" class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                                Missions
                            </a>
                            <a href="{{ path('app_calendrier') }}" class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                                Calendrier
                            </a>
                            <a href="{{ path('app_heures') }}" class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                                Heures
                            </a>
                            <a href="{{ path('app_primes') }}" class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                                Primes
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Contenu principal -->
        <main class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
            {% block body %}{% endblock %}
        </main>

        {% block javascripts %}
            {% block importmap %}{{ importmap('app') }}{% endblock %}
            <script>
                // Fonction utilitaire AJAX pour remplacer Axios
                const ajax = {
                    get: function(url, options = {}) {
                        return this.request('GET', url, null, options);
                    },
                    post: function(url, data, options = {}) {
                        return this.request('POST', url, data, options);
                    },
                    put: function(url, data, options = {}) {
                        return this.request('PUT', url, data, options);
                    },
                    delete: function(url, options = {}) {
                        return this.request('DELETE', url, null, options);
                    },
                    request: function(method, url, data, options = {}) {
                        return new Promise((resolve, reject) => {
                            const xhr = new XMLHttpRequest();
                            xhr.open(method, url);

                            // Headers par défaut
                            xhr.setRequestHeader('Content-Type', 'application/json');
                            xhr.setRequestHeader('X-Requested-With', 'XMLHttpRequest');

                            // Headers personnalisés
                            if (options.headers) {
                                Object.keys(options.headers).forEach(key => {
                                    xhr.setRequestHeader(key, options.headers[key]);
                                });
                            }

                            xhr.onload = function() {
                                const response = {
                                    status: xhr.status,
                                    statusText: xhr.statusText,
                                    data: xhr.responseText ? JSON.parse(xhr.responseText) : null
                                };

                                if (xhr.status >= 200 && xhr.status < 300) {
                                    resolve(response);
                                } else {
                                    reject(response);
                                }
                            };

                            xhr.onerror = function() {
                                reject({
                                    status: xhr.status,
                                    statusText: xhr.statusText,
                                    data: null
                                });
                            };

                            // Envoi des données
                            if (data) {
                                xhr.send(JSON.stringify(data));
                            } else {
                                xhr.send();
                            }
                        });
                    }
                };
            </script>
        {% endblock %}
    </body>
</html>

{% extends 'base.html.twig' %}

{% block title %}{{ user.nomComplet }}{% endblock %}

{% block body %}
<div class="container mx-auto px-4 py-8">
    <!-- En-tête -->
    <div class="bg-white shadow rounded-lg mb-6">
        <div class="px-4 py-5 sm:p-6">
            <div class="sm:flex sm:items-center sm:justify-between">
                <div class="sm:flex sm:items-center">
                    <div class="flex-shrink-0">
                        <div class="h-20 w-20 rounded-full bg-blue-500 flex items-center justify-center">
                            <span class="text-white text-2xl font-medium">{{ user.prenom|first }}{{ user.nom|first }}</span>
                        </div>
                    </div>
                    <div class="mt-4 sm:mt-0 sm:ml-6">
                        <h1 class="text-2xl font-bold text-gray-900">{{ user.nomComplet }}</h1>
                        <p class="text-sm text-gray-500">{{ user.role }}</p>
                        <div class="mt-2 flex items-center space-x-4">
                            <span class="text-sm text-gray-600">📧 {{ user.email }}</span>
                            {% if user.telephone %}
                                <span class="text-sm text-gray-600">📞 {{ user.telephone }}</span>
                            {% endif %}
                        </div>
                    </div>
                </div>
                <div class="mt-5 sm:mt-0">
                    {% if user.actif %}
                        <span class="inline-flex items-center px-3 py-2 rounded-full text-sm font-medium bg-green-100 text-green-800">
                            ✅ Actif
                        </span>
                    {% else %}
                        <span class="inline-flex items-center px-3 py-2 rounded-full text-sm font-medium bg-red-100 text-red-800">
                            ❌ Inactif
                        </span>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Informations détaillées -->
    <div class="grid grid-cols-1 gap-6 lg:grid-cols-3">
        <!-- Informations personnelles -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Informations</h3>
                <dl class="space-y-3">
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Horaire hebdomadaire</dt>
                        <dd class="text-sm text-gray-900">{{ user.horaireHebdo }}h/semaine</dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Type de contrat</dt>
                        <dd class="text-sm text-gray-900">
                            {% if user.forfaitJour %}
                                Forfait jour
                            {% else %}
                                Horaire
                            {% endif %}
                        </dd>
                    </div>
                    {% if user.dateEmbauche %}
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Date d'embauche</dt>
                            <dd class="text-sm text-gray-900">{{ user.dateEmbauche|date('d/m/Y') }}</dd>
                        </div>
                    {% endif %}
                    {% if user.dateDepart %}
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Date de départ</dt>
                            <dd class="text-sm text-gray-900">{{ user.dateDepart|date('d/m/Y') }}</dd>
                        </div>
                    {% endif %}
                </dl>
            </div>
        </div>

        <!-- Statistiques des heures -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Statistiques des heures</h3>
                <dl class="space-y-3">
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Total des heures</dt>
                        <dd class="text-2xl font-bold text-blue-600">{{ statsHeures.totalHeures|number_format(1) }}h</dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Nombre de semaines</dt>
                        <dd class="text-lg text-gray-900">{{ statsHeures.nombreSemaines }}</dd>
                    </div>
                    {% if statsHeures.nombreSemaines > 0 %}
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Moyenne par semaine</dt>
                            <dd class="text-lg text-gray-900">{{ (statsHeures.totalHeures / statsHeures.nombreSemaines)|number_format(1) }}h</dd>
                        </div>
                    {% endif %}
                </dl>
            </div>
        </div>

        <!-- Missions -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Missions ({{ missions|length }})</h3>
                {% if missions|length > 0 %}
                    <div class="space-y-3">
                        {% for mission in missions|slice(0, 5) %}
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-gray-900">{{ mission.titre }}</p>
                                    <p class="text-xs text-gray-500">{{ mission.pays }}</p>
                                </div>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                    {{ mission.niveau }}
                                </span>
                            </div>
                        {% endfor %}
                        {% if missions|length > 5 %}
                            <p class="text-xs text-gray-500">... et {{ missions|length - 5 }} autres</p>
                        {% endif %}
                    </div>
                {% else %}
                    <p class="text-sm text-gray-500">Aucune mission assignée</p>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Semaines de travail récentes -->
    {% if semainesTravail|length > 0 %}
        <div class="mt-6 bg-white shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Semaines de travail récentes</h3>
                <div class="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
                    <table class="min-w-full divide-y divide-gray-300">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Semaine</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Heures</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Source</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            {% for semaine in semainesTravail|slice(0, 10) %}
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ semaine.semaineAnnee }}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ semaine.heuresSaisies|number_format(1) }}h</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        {% if semaine.source == 'automatique' %}
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                                🔄 Auto
                                            </span>
                                        {% else %}
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                ✏️ Manuel
                                            </span>
                                        {% endif %}
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    {% endif %}

    <!-- Actions -->
    <div class="mt-6 flex justify-between">
        <a href="{{ path('app_users') }}" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
            ← Retour à la liste
        </a>
        <div class="space-x-3">
            <a href="{{ path('app_heures') }}?user={{ user.id }}" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                Voir les heures
            </a>
        </div>
    </div>
</div>
{% endblock %}

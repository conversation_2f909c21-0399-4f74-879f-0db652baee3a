<?php

namespace App\Controller\Api;

use App\Entity\Mission;
use App\Repository\MissionRepository;
use App\Repository\CollaborateurRepository;
use App\Service\PrimeCalculatorService;
use App\Service\ValidationService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Serializer\SerializerInterface;
use Symfony\Component\Validator\Validator\ValidatorInterface;

#[Route('/api/missions', name: 'api_mission_')]
class MissionController extends AbstractController
{
    public function __construct(
        private EntityManagerInterface $entityManager,
        private SerializerInterface $serializer,
        private ValidatorInterface $validator,
        private MissionRepository $missionRepository,
        private CollaborateurRepository $collaborateurRepository,
        private PrimeCalculatorService $primeCalculator,
        private ValidationService $validationService
    ) {}

    #[Route('', name: 'index', methods: ['GET'])]
    public function index(Request $request): JsonResponse
    {
        $zone = $request->query->get('zone');
        $pays = $request->query->get('pays');
        $collaborateurId = $request->query->get('collaborateur');
        $debut = $request->query->get('debut');
        $fin = $request->query->get('fin');

        if ($zone) {
            $missions = $this->missionRepository->findByZone($zone);
        } elseif ($pays) {
            $missions = $this->missionRepository->findByPays($pays);
        } elseif ($collaborateurId) {
            $collaborateur = $this->collaborateurRepository->find($collaborateurId);
            $missions = $collaborateur ? $this->missionRepository->findByCollaborateur($collaborateur) : [];
        } elseif ($debut && $fin) {
            $debutDate = new \DateTime($debut);
            $finDate = new \DateTime($fin);
            $missions = $this->missionRepository->findInPeriod($debutDate, $finDate);
        } else {
            $missions = $this->missionRepository->findAll();
        }

        return $this->json($missions, Response::HTTP_OK, [], [
            'groups' => ['mission:read']
        ]);
    }

    #[Route('/{id}', name: 'show', methods: ['GET'])]
    public function show(Mission $mission): JsonResponse
    {
        return $this->json($mission, Response::HTTP_OK, [], [
            'groups' => ['mission:read', 'mission:detail']
        ]);
    }

    #[Route('', name: 'create', methods: ['POST'])]
    public function create(Request $request): JsonResponse
    {
        $data = json_decode($request->getContent(), true);
        
        $collaborateur = $this->collaborateurRepository->find($data['collaborateurId'] ?? null);
        if (!$collaborateur) {
            return $this->json(['error' => 'Collaborateur non trouvé'], Response::HTTP_BAD_REQUEST);
        }

        $mission = new Mission();
        $mission->setCollaborateur($collaborateur)
                ->setTitre($data['titre'] ?? '')
                ->setPays($data['pays'] ?? '')
                ->setDateDebut(new \DateTime($data['dateDebut'] ?? 'now'))
                ->setDateFin(new \DateTime($data['dateFin'] ?? 'now'))
                ->setNiveau($data['niveau'] ?? Mission::NIVEAU_1)
                ->setZone($data['zone'] ?? Mission::ZONE_EURO);

        $errors = $this->validator->validate($mission);
        if (count($errors) > 0) {
            return $this->json(['errors' => (string) $errors], Response::HTTP_BAD_REQUEST);
        }

        // Validation métier
        $validation = $this->validationService->validerMission($mission);
        if (!$validation['valide']) {
            return $this->json([
                'errors' => $validation['erreurs'],
                'alertes' => $validation['alertes']
            ], Response::HTTP_BAD_REQUEST);
        }

        $this->entityManager->persist($mission);
        $this->entityManager->flush();

        return $this->json($mission, Response::HTTP_CREATED, [], [
            'groups' => ['mission:read']
        ]);
    }

    #[Route('/{id}', name: 'update', methods: ['PUT'])]
    public function update(Request $request, Mission $mission): JsonResponse
    {
        $data = json_decode($request->getContent(), true);

        if (isset($data['titre'])) $mission->setTitre($data['titre']);
        if (isset($data['pays'])) $mission->setPays($data['pays']);
        if (isset($data['dateDebut'])) $mission->setDateDebut(new \DateTime($data['dateDebut']));
        if (isset($data['dateFin'])) $mission->setDateFin(new \DateTime($data['dateFin']));
        if (isset($data['niveau'])) $mission->setNiveau($data['niveau']);
        if (isset($data['zone'])) $mission->setZone($data['zone']);

        if (isset($data['collaborateurId'])) {
            $collaborateur = $this->collaborateurRepository->find($data['collaborateurId']);
            if (!$collaborateur) {
                return $this->json(['error' => 'Collaborateur non trouvé'], Response::HTTP_BAD_REQUEST);
            }
            $mission->setCollaborateur($collaborateur);
        }

        $errors = $this->validator->validate($mission);
        if (count($errors) > 0) {
            return $this->json(['errors' => (string) $errors], Response::HTTP_BAD_REQUEST);
        }

        // Validation métier
        $validation = $this->validationService->validerMission($mission);
        if (!$validation['valide']) {
            return $this->json([
                'errors' => $validation['erreurs'],
                'alertes' => $validation['alertes']
            ], Response::HTTP_BAD_REQUEST);
        }

        $this->entityManager->flush();

        return $this->json($mission, Response::HTTP_OK, [], [
            'groups' => ['mission:read']
        ]);
    }

    #[Route('/{id}', name: 'delete', methods: ['DELETE'])]
    public function delete(Mission $mission): JsonResponse
    {
        $this->entityManager->remove($mission);
        $this->entityManager->flush();

        return $this->json(null, Response::HTTP_NO_CONTENT);
    }

    #[Route('/statistiques', name: 'statistiques', methods: ['GET'])]
    public function statistiques(): JsonResponse
    {
        $stats = $this->missionRepository->getStatistiques();
        return $this->json($stats);
    }

    #[Route('/{id}/primes', name: 'primes', methods: ['GET'])]
    public function primes(Mission $mission): JsonResponse
    {
        $primesTotales = $this->primeCalculator->calculPrimesTotalesMission($mission);
        
        return $this->json([
            'mission' => $mission->getTitre(),
            'primesTotales' => $primesTotales,
            'zone' => $mission->getZone(),
            'niveau' => $mission->getNiveau(),
            'dureeJours' => $mission->getDureeJours()
        ]);
    }

    #[Route('/{id}/segments', name: 'segments', methods: ['GET'])]
    public function segments(Mission $mission): JsonResponse
    {
        $segments = $mission->getSegments();
        
        return $this->json($segments, Response::HTTP_OK, [], [
            'groups' => ['segment:read']
        ]);
    }

    #[Route('/en-cours', name: 'en_cours', methods: ['GET'])]
    public function enCours(): JsonResponse
    {
        $missions = $this->missionRepository->findEnCours();
        
        return $this->json($missions, Response::HTTP_OK, [], [
            'groups' => ['mission:read']
        ]);
    }

    #[Route('/prochaines', name: 'prochaines', methods: ['GET'])]
    public function prochaines(Request $request): JsonResponse
    {
        $limit = (int) $request->query->get('limit', 10);
        $missions = $this->missionRepository->findProchaines($limit);
        
        return $this->json($missions, Response::HTTP_OK, [], [
            'groups' => ['mission:read']
        ]);
    }

    #[Route('/{id}/validation', name: 'validation', methods: ['GET'])]
    public function validation(Mission $mission): JsonResponse
    {
        $validation = $this->validationService->validerMission($mission);
        
        return $this->json($validation);
    }
}

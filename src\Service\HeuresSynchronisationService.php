<?php

namespace App\Service;

use App\Entity\User;
use App\Entity\SemaineTravail;
use App\Repository\SegmentRepository;
use App\Repository\SemaineTravailRepository;
use Doctrine\ORM\EntityManagerInterface;

class HeuresSynchronisationService
{
    public function __construct(
        private SegmentRepository $segmentRepository,
        private SemaineTravailRepository $semaineTravailRepository,
        private EntityManagerInterface $entityManager
    ) {}

    /**
     * Synchronise les heures d'un utilisateur pour une période donnée
     */
    public function synchroniserUser(
        User $user,
        ?\DateTimeInterface $dateDebut = null,
        ?\DateTimeInterface $dateFin = null,
        bool $forceOverwrite = false
    ): array {
        $dateDebut = $dateDebut ?? new \DateTime('-3 months');
        $dateFin = $dateFin ?? new \DateTime('+1 month');

        $segments = $this->segmentRepository->findByUserAndPeriod($user, $dateDebut, $dateFin);

        // Grouper les segments par semaine
        $segmentsParSemaine = $this->grouperSegmentsParSemaine($segments);

        $resultats = [];

        foreach ($segmentsParSemaine as $semaineAnnee => $segmentsSemaine) {
            $resultat = $this->synchroniserSemaine($user, $semaineAnnee, $segmentsSemaine, $forceOverwrite);
            $resultats[$semaineAnnee] = $resultat;
        }

        $this->entityManager->flush();

        return $resultats;
    }

    /**
     * Synchronise une semaine spécifique
     */
    public function synchroniserSemaine(
        User $user,
        string $semaineAnnee,
        array $segments,
        bool $forceOverwrite = false
    ): array {
        $semaineTravail = $this->semaineTravailRepository->findByUserAndSemaine($user, $semaineAnnee);

        $heuresCalculees = $this->calculerHeuresDepuisSegments($segments);

        $action = 'aucune';
        $anciennesHeures = null;

        if (!$semaineTravail) {
            // Créer une nouvelle semaine de travail
            $semaineTravail = new SemaineTravail();
            $semaineTravail->setUser($user);
            $semaineTravail->setSemaineAnnee($semaineAnnee);
            $semaineTravail->setHeuresSaisies($heuresCalculees);
            $semaineTravail->setSource('automatique');

            $this->entityManager->persist($semaineTravail);
            $action = 'creation';
        } else {
            $anciennesHeures = $semaineTravail->getHeuresSaisies();

            // Vérifier si on doit mettre à jour
            if ($forceOverwrite || $anciennesHeures == 0 || abs($anciennesHeures - $heuresCalculees) < 0.1 || $semaineTravail->isCalculeeAutomatiquement()) {
                $semaineTravail->setHeuresSaisies($heuresCalculees);
                $semaineTravail->setSource('automatique');
                $action = 'mise_a_jour';
            } else {
                $action = 'conflit';
            }
        }

        return [
            'semaine' => $semaineAnnee,
            'action' => $action,
            'heuresCalculees' => $heuresCalculees,
            'anciennesHeures' => $anciennesHeures,
            'nombreSegments' => count($segments),
            'segmentsInfo' => array_map(function($segment) {
                return [
                    'id' => $segment->getId(),
                    'type' => $segment->getType(),
                    'dateDebut' => $segment->getDateHeureDebut()->format('Y-m-d H:i'),
                    'dateFin' => $segment->getDateHeureFin()->format('Y-m-d H:i'),
                    'dureeHeures' => $segment->getDureeHeures(),
                    'missionTitre' => $segment->getMission()->getTitre()
                ];
            }, $segments)
        ];
    }

    /**
     * Calcule les heures totales à partir des segments
     */
    private function calculerHeuresDepuisSegments(array $segments): float
    {
        $totalHeures = 0;

        foreach ($segments as $segment) {
            $totalHeures += $segment->getDureeHeures();
        }

        return round($totalHeures, 2);
    }

    /**
     * Groupe les segments par semaine (format YYYY-WW)
     */
    private function grouperSegmentsParSemaine(array $segments): array
    {
        $groupes = [];

        foreach ($segments as $segment) {
            $dateDebut = $segment->getDateHeureDebut();
            if ($dateDebut) {
                $semaineAnnee = $dateDebut->format('Y-W');
                if (!isset($groupes[$semaineAnnee])) {
                    $groupes[$semaineAnnee] = [];
                }
                $groupes[$semaineAnnee][] = $segment;
            }
        }

        ksort($groupes);
        return $groupes;
    }

    /**
     * Synchronise tous les utilisateurs
     */
    public function synchroniserTous(
        ?\DateTimeInterface $dateDebut = null,
        ?\DateTimeInterface $dateFin = null,
        bool $forceOverwrite = false
    ): array {
        $users = $this->entityManager->getRepository(User::class)->findActifs();
        $resultats = [];

        foreach ($users as $user) {
            $resultats[$user->getId()] = [
                'userId' => $user->getId(),
                'userNom' => $user->getNomComplet(),
                'resultats' => $this->synchroniserUser($user, $dateDebut, $dateFin, $forceOverwrite)
            ];
        }

        return $resultats;
    }

    /**
     * Obtient un aperçu des synchronisations sans les appliquer
     */
    public function previewSynchronisation(
        User $user,
        ?\DateTimeInterface $dateDebut = null,
        ?\DateTimeInterface $dateFin = null
    ): array {
        $dateDebut = $dateDebut ?? new \DateTime('-3 months');
        $dateFin = $dateFin ?? new \DateTime('+1 month');

        $segments = $this->segmentRepository->findByUserAndPeriod($user, $dateDebut, $dateFin);
        $segmentsParSemaine = $this->grouperSegmentsParSemaine($segments);

        $preview = [];

        foreach ($segmentsParSemaine as $semaineAnnee => $segmentsSemaine) {
            $semaineTravail = $this->semaineTravailRepository->findByUserAndSemaine($user, $semaineAnnee);
            $heuresCalculees = $this->calculerHeuresDepuisSegments($segmentsSemaine);

            $preview[$semaineAnnee] = [
                'heuresActuelles' => $semaineTravail ? $semaineTravail->getHeuresSaisies() : 0,
                'heuresCalculees' => $heuresCalculees,
                'difference' => $heuresCalculees - ($semaineTravail ? $semaineTravail->getHeuresSaisies() : 0),
                'nombreSegments' => count($segmentsSemaine),
                'conflit' => $semaineTravail && $semaineTravail->getHeuresSaisies() > 0 && abs($semaineTravail->getHeuresSaisies() - $heuresCalculees) > 0.1
            ];
        }

        return $preview;
    }
}

<?php

namespace App\Service;

use App\Entity\Collaborateur;
use App\Entity\SemaineTravail;
use App\Repository\SegmentRepository;
use App\Repository\SemaineTravailRepository;
use Doctrine\ORM\EntityManagerInterface;

class HeuresSynchronisationService
{
    public function __construct(
        private SegmentRepository $segmentRepository,
        private SemaineTravailRepository $semaineTravailRepository,
        private EntityManagerInterface $entityManager
    ) {}

    /**
     * Synchronise les heures d'un collaborateur pour une période donnée
     */
    public function synchroniserCollaborateur(
        Collaborateur $collaborateur,
        ?\DateTimeInterface $dateDebut = null,
        ?\DateTimeInterface $dateFin = null,
        bool $forceOverwrite = false
    ): array {
        $dateDebut = $dateDebut ?? new \DateTime('-3 months');
        $dateFin = $dateFin ?? new \DateTime('+1 month');

        $segments = $this->segmentRepository->findByCollaborateurAndPeriod($collaborateur, $dateDebut, $dateFin);

        // Grouper les segments par semaine
        $segmentsParSemaine = $this->grouperSegmentsParSemaine($segments);

        $resultats = [];

        foreach ($segmentsParSemaine as $semaineAnnee => $segmentsSemaine) {
            $resultat = $this->synchroniserSemaine($collaborateur, $semaineAnnee, $segmentsSemaine, $forceOverwrite);
            $resultats[$semaineAnnee] = $resultat;
        }

        $this->entityManager->flush();

        return $resultats;
    }

    /**
     * Synchronise une semaine spécifique
     */
    public function synchroniserSemaine(
        Collaborateur $collaborateur,
        string $semaineAnnee,
        array $segments,
        bool $forceOverwrite = false
    ): array {
        $semaineTravail = $this->semaineTravailRepository->findByCollaborateurAndSemaine($collaborateur, $semaineAnnee);

        $heuresCalculees = $this->calculerHeuresDepuisSegments($segments);

        $action = 'aucune';
        $anciennesHeures = null;

        if (!$semaineTravail) {
            // Créer une nouvelle semaine de travail
            $semaineTravail = new SemaineTravail();
            $semaineTravail->setCollaborateur($collaborateur);
            $semaineTravail->setSemaineAnnee($semaineAnnee);
            $semaineTravail->setHeuresSaisies($heuresCalculees);
            $semaineTravail->setSource('automatique');

            $this->entityManager->persist($semaineTravail);
            $action = 'creation';
        } else {
            $anciennesHeures = $semaineTravail->getHeuresSaisies();

            // Vérifier si on doit mettre à jour
            if ($forceOverwrite || $anciennesHeures == 0 || abs($anciennesHeures - $heuresCalculees) < 0.1 || $semaineTravail->isCalculeeAutomatiquement()) {
                $semaineTravail->setHeuresSaisies($heuresCalculees);
                $semaineTravail->setSource('automatique');
                $action = 'mise_a_jour';
            } else {
                $action = 'conflit';
            }
        }

        return [
            'semaine' => $semaineAnnee,
            'action' => $action,
            'heuresCalculees' => $heuresCalculees,
            'anciennesHeures' => $anciennesHeures,
            'nombreSegments' => count($segments),
            'segments' => $segments
        ];
    }

    /**
     * Calcule les heures totales à partir des segments
     */
    private function calculerHeuresDepuisSegments(array $segments): float
    {
        $totalHeures = 0;

        foreach ($segments as $segment) {
            $totalHeures += $segment->getDureeHeures();
        }

        return round($totalHeures, 2);
    }

    /**
     * Groupe les segments par semaine (format YYYY-WW)
     */
    private function grouperSegmentsParSemaine(array $segments): array
    {
        $groupes = [];

        foreach ($segments as $segment) {
            $dateDebut = $segment->getDateHeureDebut();
            if ($dateDebut) {
                $semaineAnnee = $dateDebut->format('Y-W');
                if (!isset($groupes[$semaineAnnee])) {
                    $groupes[$semaineAnnee] = [];
                }
                $groupes[$semaineAnnee][] = $segment;
            }
        }

        ksort($groupes);
        return $groupes;
    }

    /**
     * Synchronise tous les collaborateurs
     */
    public function synchroniserTous(
        ?\DateTimeInterface $dateDebut = null,
        ?\DateTimeInterface $dateFin = null,
        bool $forceOverwrite = false
    ): array {
        $collaborateurs = $this->entityManager->getRepository(Collaborateur::class)->findAll();
        $resultats = [];

        foreach ($collaborateurs as $collaborateur) {
            $resultats[$collaborateur->getId()] = [
                'collaborateur' => $collaborateur->getNomComplet(),
                'resultats' => $this->synchroniserCollaborateur($collaborateur, $dateDebut, $dateFin, $forceOverwrite)
            ];
        }

        return $resultats;
    }

    /**
     * Obtient un aperçu des synchronisations sans les appliquer
     */
    public function previewSynchronisation(
        Collaborateur $collaborateur,
        ?\DateTimeInterface $dateDebut = null,
        ?\DateTimeInterface $dateFin = null
    ): array {
        $dateDebut = $dateDebut ?? new \DateTime('-3 months');
        $dateFin = $dateFin ?? new \DateTime('+1 month');

        $segments = $this->segmentRepository->findByCollaborateurAndPeriod($collaborateur, $dateDebut, $dateFin);
        $segmentsParSemaine = $this->grouperSegmentsParSemaine($segments);

        $preview = [];

        foreach ($segmentsParSemaine as $semaineAnnee => $segmentsSemaine) {
            $semaineTravail = $this->semaineTravailRepository->findByCollaborateurAndSemaine($collaborateur, $semaineAnnee);
            $heuresCalculees = $this->calculerHeuresDepuisSegments($segmentsSemaine);

            $preview[$semaineAnnee] = [
                'heuresActuelles' => $semaineTravail ? $semaineTravail->getHeuresSaisies() : 0,
                'heuresCalculees' => $heuresCalculees,
                'difference' => $heuresCalculees - ($semaineTravail ? $semaineTravail->getHeuresSaisies() : 0),
                'nombreSegments' => count($segmentsSemaine),
                'conflit' => $semaineTravail && $semaineTravail->getHeuresSaisies() > 0 && abs($semaineTravail->getHeuresSaisies() - $heuresCalculees) > 0.1
            ];
        }

        return $preview;
    }
}

<?php

namespace App\Repository;

use App\Entity\SemaineTravail;
use App\Entity\Collaborateur;
use App\Entity\User;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<SemaineTravail>
 */
class SemaineTravailRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, SemaineTravail::class);
    }

    public function save(SemaineTravail $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(SemaineTravail $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    /**
     * Trouve les semaines de travail d'un collaborateur
     */
    public function findByCollaborateur(Collaborateur $collaborateur): array
    {
        return $this->createQueryBuilder('st')
            ->andWhere('st.collaborateur = :collaborateur')
            ->setParameter('collaborateur', $collaborateur)
            ->orderBy('st.semaineAnnee', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Trouve une semaine de travail spécifique (ancienne méthode)
     */
    public function findByCollaborateurAndSemaine(Collaborateur $collaborateur, string $semaineAnnee): ?SemaineTravail
    {
        return $this->createQueryBuilder('st')
            ->andWhere('st.collaborateur = :collaborateur')
            ->andWhere('st.semaineAnnee = :semaineAnnee')
            ->setParameter('collaborateur', $collaborateur)
            ->setParameter('semaineAnnee', $semaineAnnee)
            ->getQuery()
            ->getOneOrNullResult();
    }

    /**
     * Trouve une semaine de travail par utilisateur et semaine
     */
    public function findByUserAndSemaine(User $user, string $semaineAnnee): ?SemaineTravail
    {
        return $this->createQueryBuilder('st')
            ->andWhere('st.user = :user')
            ->andWhere('st.semaineAnnee = :semaineAnnee')
            ->setParameter('user', $user)
            ->setParameter('semaineAnnee', $semaineAnnee)
            ->getQuery()
            ->getOneOrNullResult();
    }

    /**
     * Trouve les semaines de travail pour une période
     */
    public function findInPeriod(string $semaineDebut, string $semaineFin): array
    {
        return $this->createQueryBuilder('st')
            ->andWhere('st.semaineAnnee BETWEEN :debut AND :fin')
            ->setParameter('debut', $semaineDebut)
            ->setParameter('fin', $semaineFin)
            ->orderBy('st.semaineAnnee', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Trouve les semaines avec heures supplémentaires
     */
    public function findWithHeuresSupplementaires(): array
    {
        return $this->createQueryBuilder('st')
            ->join('st.collaborateur', 'c')
            ->andWhere('st.heuresSaisies > c.horaireHebdo')
            ->orderBy('st.semaineAnnee', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Calcule le total d'heures pour un collaborateur sur une période
     */
    public function getTotalHeures(Collaborateur $collaborateur, string $semaineDebut = null, string $semaineFin = null): float
    {
        $qb = $this->createQueryBuilder('st')
            ->select('SUM(st.heuresSaisies)')
            ->andWhere('st.collaborateur = :collaborateur')
            ->setParameter('collaborateur', $collaborateur);

        if ($semaineDebut) {
            $qb->andWhere('st.semaineAnnee >= :debut')
               ->setParameter('debut', $semaineDebut);
        }

        if ($semaineFin) {
            $qb->andWhere('st.semaineAnnee <= :fin')
               ->setParameter('fin', $semaineFin);
        }

        return (float) $qb->getQuery()->getSingleScalarResult();
    }

    /**
     * Calcule le total d'heures supplémentaires pour un collaborateur
     */
    public function getTotalHeuresSupplementaires(Collaborateur $collaborateur, string $semaineDebut = null, string $semaineFin = null): float
    {
        $qb = $this->createQueryBuilder('st')
            ->select('SUM(GREATEST(0, st.heuresSaisies - c.horaireHebdo))')
            ->join('st.collaborateur', 'c')
            ->andWhere('st.collaborateur = :collaborateur')
            ->setParameter('collaborateur', $collaborateur);

        if ($semaineDebut) {
            $qb->andWhere('st.semaineAnnee >= :debut')
               ->setParameter('debut', $semaineDebut);
        }

        if ($semaineFin) {
            $qb->andWhere('st.semaineAnnee <= :fin')
               ->setParameter('fin', $semaineFin);
        }

        return (float) $qb->getQuery()->getSingleScalarResult();
    }

    /**
     * Statistiques des heures par semaine
     */
    public function getStatistiquesHebdomadaires(): array
    {
        return $this->createQueryBuilder('st')
            ->select('st.semaineAnnee, AVG(st.heuresSaisies) as moyenneHeures, SUM(st.heuresSaisies) as totalHeures, COUNT(st.id) as nombreCollaborateurs')
            ->groupBy('st.semaineAnnee')
            ->orderBy('st.semaineAnnee', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Trouve ou crée une semaine de travail
     */
    public function findOrCreate(Collaborateur $collaborateur, string $semaineAnnee): SemaineTravail
    {
        $semaineTravail = $this->findByCollaborateurAndSemaine($collaborateur, $semaineAnnee);

        if (!$semaineTravail) {
            $semaineTravail = new SemaineTravail();
            $semaineTravail->setCollaborateur($collaborateur);
            $semaineTravail->setSemaineAnnee($semaineAnnee);
            $semaineTravail->setHeuresSaisies(0);
        }

        return $semaineTravail;
    }

    /**
     * Trouve les semaines de travail avec filtres avancés
     */
    public function findWithFilters(array $criteria): array
    {
        $qb = $this->createQueryBuilder('st')
            ->leftJoin('st.user', 'u')
            ->leftJoin('st.collaborateur', 'c')
            ->addSelect('u', 'c');

        if (isset($criteria['user'])) {
            $qb->andWhere('st.user = :user')
               ->setParameter('user', $criteria['user']);
        }

        // Compatibilité avec l'ancien système
        if (isset($criteria['collaborateur'])) {
            $qb->andWhere('st.collaborateur = :collaborateur')
               ->setParameter('collaborateur', $criteria['collaborateur']);
        }

        if (isset($criteria['semaineDebut'])) {
            $qb->andWhere('st.semaineAnnee >= :debut')
               ->setParameter('debut', $criteria['semaineDebut']);
        }

        if (isset($criteria['semaineFin'])) {
            $qb->andWhere('st.semaineAnnee <= :fin')
               ->setParameter('fin', $criteria['semaineFin']);
        }

        if (isset($criteria['typeContrat'])) {
            if ($criteria['typeContrat'] === 'forfait') {
                $qb->andWhere('(u.forfaitJour = :forfaitJour OR c.forfaitJour = :forfaitJour)')
                   ->setParameter('forfaitJour', true);
            } elseif ($criteria['typeContrat'] === 'horaire') {
                $qb->andWhere('(u.forfaitJour = :forfaitJour OR c.forfaitJour = :forfaitJour)')
                   ->setParameter('forfaitJour', false);
            }
        }

        return $qb->orderBy('st.semaineAnnee', 'DESC')
                  ->addOrderBy('COALESCE(u.nom, c.nom)', 'ASC')
                  ->getQuery()
                  ->getResult();
    }
}

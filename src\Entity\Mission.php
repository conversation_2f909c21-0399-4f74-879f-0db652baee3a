<?php

namespace App\Entity;


use App\Repository\MissionRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Serializer\Annotation\Groups;

#[ORM\Entity(repositoryClass: MissionRepository::class)]

class Mission
{
    public const NIVEAU_1 = 1;
    public const NIVEAU_2 = 2;

    public const ZONE_EURO = 'EURO';
    public const ZONE_HORS_EURO = 'HORS_EURO';

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    #[Groups(['mission:read'])]
    private ?int $id = null;

    #[ORM\ManyToOne(inversedBy: 'missions')]
    #[ORM\JoinColumn(nullable: false)]
    #[Groups(['mission:read', 'mission:write'])]
    private ?Client $client = null;

    #[ORM\ManyToOne(inversedBy: 'missions')]
    #[ORM\JoinColumn(nullable: true)]
    #[Groups(['mission:read', 'mission:write'])]
    private ?Site $site = null;

    /**
     * @var Collection<int, User>
     */
    #[ORM\ManyToMany(targetEntity: User::class, inversedBy: 'missions')]
    #[ORM\JoinTable(name: 'mission_user')]
    #[Groups(['mission:read', 'mission:write'])]
    private Collection $users;



    #[ORM\Column(length: 255)]
    #[Assert\NotBlank]
    #[Assert\Length(max: 255)]
    #[Groups(['mission:read', 'mission:write'])]
    private ?string $titre = null;

    #[ORM\Column(length: 100)]
    #[Assert\NotBlank]
    #[Assert\Length(max: 100)]
    #[Groups(['mission:read', 'mission:write'])]
    private ?string $pays = null;

    #[ORM\Column(type: Types::DATE_MUTABLE)]
    #[Assert\NotNull]
    #[Groups(['mission:read', 'mission:write'])]
    private ?\DateTimeInterface $dateDebut = null;

    #[ORM\Column(type: Types::DATE_MUTABLE)]
    #[Assert\NotNull]
    #[Groups(['mission:read', 'mission:write'])]
    private ?\DateTimeInterface $dateFin = null;

    #[ORM\Column]
    #[Assert\NotNull]
    #[Assert\Choice(choices: [self::NIVEAU_1, self::NIVEAU_2])]
    #[Groups(['mission:read', 'mission:write'])]
    private ?int $niveau = null;

    #[ORM\Column(length: 20)]
    #[Assert\NotBlank]
    #[Assert\Choice(choices: [self::ZONE_EURO, self::ZONE_HORS_EURO])]
    #[Groups(['mission:read', 'mission:write'])]
    private ?string $zone = null;

    /**
     * @var Collection<int, Segment>
     */
    #[ORM\OneToMany(targetEntity: Segment::class, mappedBy: 'mission', orphanRemoval: true)]
    #[Groups(['mission:detail'])]
    private Collection $segments;

    public function __construct()
    {
        $this->segments = new ArrayCollection();
        $this->users = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }



    public function getTitre(): ?string
    {
        return $this->titre;
    }

    public function setTitre(string $titre): static
    {
        $this->titre = $titre;
        return $this;
    }

    public function getPays(): ?string
    {
        return $this->pays;
    }

    public function setPays(string $pays): static
    {
        $this->pays = $pays;
        return $this;
    }

    public function getDateDebut(): ?\DateTimeInterface
    {
        return $this->dateDebut;
    }

    public function setDateDebut(\DateTimeInterface $dateDebut): static
    {
        $this->dateDebut = $dateDebut;
        return $this;
    }

    public function getDateFin(): ?\DateTimeInterface
    {
        return $this->dateFin;
    }

    public function setDateFin(\DateTimeInterface $dateFin): static
    {
        $this->dateFin = $dateFin;
        return $this;
    }

    public function getNiveau(): ?int
    {
        return $this->niveau;
    }

    public function setNiveau(int $niveau): static
    {
        $this->niveau = $niveau;
        return $this;
    }

    public function getZone(): ?string
    {
        return $this->zone;
    }

    public function setZone(string $zone): static
    {
        $this->zone = $zone;
        return $this;
    }

    /**
     * @return Collection<int, Segment>
     */
    public function getSegments(): Collection
    {
        return $this->segments;
    }

    public function addSegment(Segment $segment): static
    {
        if (!$this->segments->contains($segment)) {
            $this->segments->add($segment);
            $segment->setMission($this);
        }
        return $this;
    }

    public function removeSegment(Segment $segment): static
    {
        if ($this->segments->removeElement($segment)) {
            if ($segment->getMission() === $this) {
                $segment->setMission(null);
            }
        }
        return $this;
    }

    public function getDureeJours(): int
    {
        if (!$this->dateDebut || !$this->dateFin) {
            return 0;
        }
        return $this->dateDebut->diff($this->dateFin)->days + 1;
    }

    public function isZoneEuro(): bool
    {
        return $this->zone === self::ZONE_EURO;
    }

    public function getClient(): ?Client
    {
        return $this->client;
    }

    public function setClient(?Client $client): static
    {
        $this->client = $client;
        return $this;
    }

    public function getSite(): ?Site
    {
        return $this->site;
    }

    public function setSite(?Site $site): static
    {
        $this->site = $site;
        return $this;
    }

    /**
     * @return Collection<int, User>
     */
    public function getUsers(): Collection
    {
        return $this->users;
    }

    public function addUser(User $user): static
    {
        if (!$this->users->contains($user)) {
            $this->users->add($user);
        }
        return $this;
    }

    public function removeUser(User $user): static
    {
        $this->users->removeElement($user);
        return $this;
    }

    public function hasUser(User $user): bool
    {
        return $this->users->contains($user);
    }

    public function getUsersNoms(): string
    {
        return implode(', ', $this->users->map(fn(User $user) => $user->getNomComplet())->toArray());
    }

    public function __toString(): string
    {
        return $this->titre ?? 'Mission #' . $this->id;
    }
}

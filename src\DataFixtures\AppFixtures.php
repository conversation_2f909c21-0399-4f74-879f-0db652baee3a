<?php

namespace App\DataFixtures;

use App\Entity\Collaborateur;
use App\Entity\Mission;
use App\Entity\Segment;
use App\Entity\SemaineTravail;
use Doctrine\Bundle\FixturesBundle\Fixture;
use Doctrine\Persistence\ObjectManager;

class AppFixtures extends Fixture
{
    public function load(ObjectManager $manager): void
    {
        // Créer des collaborateurs
        $collaborateurs = [];
        
        $collaborateur1 = new Collaborateur();
        $collaborateur1->setNom('Dupont')
                      ->setPrenom('Jean')
                      ->setEmail('<EMAIL>')
                      ->setRole('Manager')
                      ->setHoraireHebdo(35)
                      ->setForfaitJour(false);
        $manager->persist($collaborateur1);
        $collaborateurs[] = $collaborateur1;

        $collaborateur2 = new Collaborateur();
        $collaborateur2->setNom('Martin')
                      ->setPrenom('Marie')
                      ->setEmail('<EMAIL>')
                      ->setRole('Consultant')
                      ->setHoraireHebdo(35)
                      ->setForfaitJour(true);
        $manager->persist($collaborateur2);
        $collaborateurs[] = $collaborateur2;

        $collaborateur3 = new Collaborateur();
        $collaborateur3->setNom('Bernard')
                      ->setPrenom('Pierre')
                      ->setEmail('<EMAIL>')
                      ->setRole('Expert')
                      ->setHoraireHebdo(39)
                      ->setForfaitJour(false);
        $manager->persist($collaborateur3);
        $collaborateurs[] = $collaborateur3;

        $collaborateur4 = new Collaborateur();
        $collaborateur4->setNom('Leroy')
                      ->setPrenom('Sophie')
                      ->setEmail('<EMAIL>')
                      ->setRole('Technicien')
                      ->setHoraireHebdo(35)
                      ->setForfaitJour(false);
        $manager->persist($collaborateur4);
        $collaborateurs[] = $collaborateur4;

        // Créer des missions
        $missions = [];

        $mission1 = new Mission();
        $mission1->setCollaborateur($collaborateur1)
                 ->setTitre('Audit sécurité - Banque Centrale')
                 ->setPays('France')
                 ->setDateDebut(new \DateTime('2024-01-15'))
                 ->setDateFin(new \DateTime('2024-01-25'))
                 ->setNiveau(Mission::NIVEAU_2)
                 ->setZone(Mission::ZONE_EURO);
        $manager->persist($mission1);
        $missions[] = $mission1;

        $mission2 = new Mission();
        $mission2->setCollaborateur($collaborateur2)
                 ->setTitre('Formation cybersécurité')
                 ->setPays('Allemagne')
                 ->setDateDebut(new \DateTime('2024-02-01'))
                 ->setDateFin(new \DateTime('2024-02-05'))
                 ->setNiveau(Mission::NIVEAU_1)
                 ->setZone(Mission::ZONE_EURO);
        $manager->persist($mission2);
        $missions[] = $mission2;

        $mission3 = new Mission();
        $mission3->setCollaborateur($collaborateur3)
                 ->setTitre('Implémentation système - Tokyo')
                 ->setPays('Japon')
                 ->setDateDebut(new \DateTime('2024-02-10'))
                 ->setDateFin(new \DateTime('2024-02-20'))
                 ->setNiveau(Mission::NIVEAU_2)
                 ->setZone(Mission::ZONE_HORS_EURO);
        $manager->persist($mission3);
        $missions[] = $mission3;

        $mission4 = new Mission();
        $mission4->setCollaborateur($collaborateur4)
                 ->setTitre('Maintenance serveurs')
                 ->setPays('Espagne')
                 ->setDateDebut(new \DateTime('2024-01-20'))
                 ->setDateFin(new \DateTime('2024-01-22'))
                 ->setNiveau(Mission::NIVEAU_1)
                 ->setZone(Mission::ZONE_EURO);
        $manager->persist($mission4);
        $missions[] = $mission4;

        // Créer des segments pour les missions
        
        // Mission 1 - Segments
        $segment1 = new Segment();
        $segment1->setMission($mission1)
                 ->setType(Segment::TYPE_VOYAGE)
                 ->setDateHeureDebut(new \DateTime('2024-01-15 06:00'))
                 ->setDateHeureFin(new \DateTime('2024-01-15 10:00'));
        $manager->persist($segment1);

        $segment2 = new Segment();
        $segment2->setMission($mission1)
                 ->setType(Segment::TYPE_INTERVENTION)
                 ->setDateHeureDebut(new \DateTime('2024-01-15 14:00'))
                 ->setDateHeureFin(new \DateTime('2024-01-15 18:00'));
        $manager->persist($segment2);

        $segment3 = new Segment();
        $segment3->setMission($mission1)
                 ->setType(Segment::TYPE_STAND_BY)
                 ->setDateHeureDebut(new \DateTime('2024-01-16 20:00'))
                 ->setDateHeureFin(new \DateTime('2024-01-17 08:00'));
        $manager->persist($segment3);

        // Mission 2 - Segments
        $segment4 = new Segment();
        $segment4->setMission($mission2)
                 ->setType(Segment::TYPE_VOYAGE)
                 ->setDateHeureDebut(new \DateTime('2024-02-01 07:00'))
                 ->setDateHeureFin(new \DateTime('2024-02-01 09:00'));
        $manager->persist($segment4);

        $segment5 = new Segment();
        $segment5->setMission($mission2)
                 ->setType(Segment::TYPE_INTERVENTION)
                 ->setDateHeureDebut(new \DateTime('2024-02-01 09:00'))
                 ->setDateHeureFin(new \DateTime('2024-02-01 17:00'));
        $manager->persist($segment5);

        // Mission 3 - Segments (voyage long avec décalage horaire)
        $segment6 = new Segment();
        $segment6->setMission($mission3)
                 ->setType(Segment::TYPE_VOYAGE)
                 ->setDateHeureDebut(new \DateTime('2024-02-09 22:00'))
                 ->setDateHeureFin(new \DateTime('2024-02-10 14:00'));
        $manager->persist($segment6);

        $segment7 = new Segment();
        $segment7->setMission($mission3)
                 ->setType(Segment::TYPE_INTERVENTION)
                 ->setDateHeureDebut(new \DateTime('2024-02-11 09:00'))
                 ->setDateHeureFin(new \DateTime('2024-02-11 18:00'));
        $manager->persist($segment7);

        // Créer des semaines de travail
        $semaines = ['2024-03', '2024-04', '2024-05', '2024-06'];
        
        foreach ($collaborateurs as $collaborateur) {
            foreach ($semaines as $semaine) {
                $semaineTravail = new SemaineTravail();
                $semaineTravail->setCollaborateur($collaborateur)
                              ->setSemaineAnnee($semaine)
                              ->setHeuresSaisies($this->getRandomHours($collaborateur->getHoraireHebdo()));
                $manager->persist($semaineTravail);
            }
        }

        $manager->flush();
    }

    private function getRandomHours(float $horaireHebdo): float
    {
        // Générer des heures aléatoirement autour de l'horaire hebdomadaire
        $variation = rand(-5, 15); // Variation de -5 à +15 heures
        return max(0, $horaireHebdo + $variation);
    }
}

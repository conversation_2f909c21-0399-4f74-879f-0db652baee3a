# OSI Manager

Application de gestion des missions, collaborateurs et calcul des primes pour OSI.

## 🚀 Fonctionnalités

### Gestion des Collaborateurs
- ✅ CRUD complet des collaborateurs
- ✅ Gestion des horaires hebdomadaires (35h, 39h, etc.)
- ✅ Support forfait jour / horaire
- ✅ Statistiques et recherche

### Gestion des Missions
- ✅ CRUD des missions avec dates, pays, zone (Euro/Hors Euro)
- ✅ Niveaux 1 et 2 pour le calcul des primes
- ✅ Association collaborateur-mission
- ✅ Suivi des statuts (en cours, prochaines, terminées)

### Gestion des Segments
- ✅ Types : VOYAGE, INTERVENTION, STAND_BY
- ✅ Calendrier interactif avec FullCalendar
- ✅ Drag & drop pour créer/modifier
- ✅ Validation des chevauchements et repos

### Calcul des Heures
- ✅ Heures normales vs supplémentaires
- ✅ Majorations x1.25 et x1.50
- ✅ Saisie hebdomadaire
- ✅ Statistiques par collaborateur

### Calcul des Primes
- ✅ Barème complet Zone Euro / Hors Zone Euro
- ✅ Primes différenciées : semaine, week-end travaillé/non travaillé, voyage
- ✅ Calcul automatique par mission
- ✅ Rapports et statistiques

### Calcul de Récupération
- ✅ Mode horaire : heures hors plages normales (09:00-12:00, 14:00-18:00)
- ✅ Mode forfait jour : 0.5 ou 1 jour selon durée
- ✅ Prise en compte week-ends et jours fériés

### Validation Métier
- ✅ Validation des repos post-voyage (11h minimum si voyage ≥10h ou décalage ≥5h)
- ✅ Détection des chevauchements
- ✅ Alertes et erreurs contextuelles

## 🛠 Stack Technique

- **Backend** : Symfony 7.3
- **Base de données** : MySQL 8.0+
- **Frontend** : Twig + Tailwind CSS + AJAX natif
- **Calendrier** : FullCalendar 6.1
- **API** : Contrôleurs REST personnalisés

## 📦 Installation

```bash
# Cloner le projet
git clone <repository-url>
cd OSI

# Installer les dépendances
composer install

# Configurer la base de données MySQL
# Éditer .env avec vos paramètres MySQL :
# DATABASE_URL="mysql://user:password@127.0.0.1:3306/osi_manager?serverVersion=8.0&charset=utf8mb4"

# Créer la base de données
php bin/console doctrine:database:create
php bin/console doctrine:schema:create

# Ou utiliser le script de réinitialisation
# Windows PowerShell :
powershell -ExecutionPolicy Bypass -File scripts/reset-database.ps1
# Linux/Mac :
chmod +x scripts/reset-database.sh && ./scripts/reset-database.sh

# Charger les données de test
curl http://localhost:8000/load-test-data

# Démarrer le serveur
php -S localhost:8000 -t public
```

## 🌐 URLs Principales

- **Dashboard** : http://localhost:8000/
- **Collaborateurs** : http://localhost:8000/collaborateurs
- **Missions** : http://localhost:8000/missions
- **Calendrier** : http://localhost:8000/calendrier
- **Heures** : http://localhost:8000/heures
- **Primes** : http://localhost:8000/primes

## 🔌 API REST

### Endpoints principaux

```
GET    /api/collaborateurs           # Liste des collaborateurs
POST   /api/collaborateurs           # Créer un collaborateur
GET    /api/collaborateurs/{id}      # Détail d'un collaborateur
PUT    /api/collaborateurs/{id}      # Modifier un collaborateur
DELETE /api/collaborateurs/{id}      # Supprimer un collaborateur

GET    /api/missions                 # Liste des missions
POST   /api/missions                 # Créer une mission
GET    /api/missions/{id}/primes     # Calcul des primes d'une mission

GET    /api/segments                 # Liste des segments
POST   /api/segments                 # Créer un segment
GET    /api/segments/calendar        # Données pour le calendrier

GET    /api/semaines-travail         # Semaines de travail
POST   /api/semaines-travail         # Saisir des heures
```



## 🧮 Barème des Primes

### Zone Euro
| Type | Niveau 1 | Niveau 2 |
|------|----------|----------|
| Semaine | 130€ | 230€ |
| WE travaillé | 160€ | 335€ |
| WE non travaillé | 90€ | 180€ |
| WE voyage | 160€ | 160€ |

### Hors Zone Euro
| Type | Niveau 1 | Niveau 2 |
|------|----------|----------|
| Semaine | 150€ | 250€ |
| WE travaillé | 180€ | 355€ |
| WE non travaillé | 110€ | 200€ |
| WE voyage | 180€ | 180€ |

## ⚙️ Configuration

### Heures Supplémentaires
- **Majoration x1.25** : jusqu'à 43h/semaine
- **Majoration x1.50** : au-delà de 43h/semaine

### Récupération
- **Mode horaire** : heures VOYAGE hors plages 09:00-12:00 et 14:00-18:00
- **Mode forfait jour** : ≤4h → 0.5 jour, >4h → 1 jour

### Validation Repos
- **Voyage ≥10h OU décalage ≥5h** → repos minimum 11h

## 🧪 Tests

```bash
# Tests des services
curl http://localhost:8000/test-services

# Tests de l'API
curl http://localhost:8000/test-api

# Tests du calcul des primes
curl http://localhost:8000/test-primes-calculation

# Tests du calendrier
curl http://localhost:8000/test-calendar-data

# Tests de la base de données
curl http://localhost:8000/database-test-connection
curl http://localhost:8000/database-info
```

## 📁 Structure du Projet

```
src/
├── Controller/          # Contrôleurs web et API
├── Entity/             # Entités Doctrine
├── Repository/         # Repositories Doctrine
├── Service/            # Services métiers
└── DataFixtures/       # Données de test

templates/              # Templates Twig
├── base.html.twig
├── dashboard/
├── collaborateurs/
├── missions/
├── calendrier/
├── heures/
└── primes/

translations/           # Fichiers de traduction FR/EN
```

## 🔧 Services Métiers

### PrimeCalculatorService
- Calcul des primes selon le barème
- Détermination du type de jour
- Calcul par mission et par période

### HeureCalculatorService
- Calcul heures normales/supplémentaires
- Majorations x1.25 et x1.50
- Statistiques par collaborateur

### RecuperationCalculatorService
- Calcul récupération mode horaire/forfait jour
- Prise en compte plages normales
- Gestion week-ends et jours fériés

### ValidationService
- Validation repos post-voyage
- Détection chevauchements
- Validation contraintes métier

## 🌍 Internationalisation

Support FR/EN avec Symfony Translator :
- Fichiers : `translations/messages.fr.yaml` et `translations/messages.en.yaml`
- Usage : `{{ 'nav.dashboard'|trans }}`

## 📱 Interface Utilisateur

- **Design** : Tailwind CSS avec composants réutilisables
- **Responsive** : Mobile-first design
- **Calendrier** : FullCalendar avec drag & drop
- **Modals** : Formulaires en overlay
- **AJAX** : XMLHttpRequest natif pour les appels API

## 🚀 Déploiement

### Production
1. Configurer MySQL dans `.env.prod`
2. `composer install --no-dev --optimize-autoloader`
3. `php bin/console doctrine:migrations:migrate`
4. Configurer serveur web (Apache/Nginx)

### Docker (optionnel)
```dockerfile
FROM php:8.2-apache
# Configuration Docker...
```

## 📄 Licence

Propriétaire - OSI Manager © 2024

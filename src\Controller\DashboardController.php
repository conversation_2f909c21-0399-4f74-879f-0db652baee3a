<?php

namespace App\Controller;

use App\Repository\UserRepository;
use App\Repository\MissionRepository;
use App\Repository\SegmentRepository;
use App\Repository\SemaineTravailRepository;
use App\Service\PrimeCalculatorService;
use App\Service\HeureCalculatorService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

class DashboardController extends AbstractController
{
    public function __construct(
        private UserRepository $userRepository,
        private MissionRepository $missionRepository,
        private SegmentRepository $segmentRepository,
        private SemaineTravailRepository $semaineTravailRepository,
        private PrimeCalculatorService $primeCalculator,
        private HeureCalculatorService $heureCalculator
    ) {}

    #[Route('/', name: 'app_dashboard')]
    public function index(): Response
    {
        // Statistiques générales
        $statsUsers = $this->userRepository->findActifs();
        $statsMissions = $this->missionRepository->getStatistiques();

        // Missions en cours et prochaines
        $missionsEnCours = $this->missionRepository->findEnCours();
        $prochaines = $this->missionRepository->findProchaines(5);

        // Utilisateurs actifs
        $usersActifs = $this->userRepository->findWithMissions();

        return $this->render('dashboard/index.html.twig', [
            'statsUsers' => ['total' => count($statsUsers)],
            'statsMissions' => $statsMissions,
            'missionsEnCours' => $missionsEnCours,
            'prochaines' => $prochaines,
            'usersActifs' => $usersActifs,
            // Compatibilité avec l'ancien template
            'statsCollaborateurs' => ['total' => count($statsUsers)],
            'collaborateursActifs' => $usersActifs,
        ]);
    }

    #[Route('/users', name: 'app_users')]
    public function users(): Response
    {
        $users = $this->userRepository->findActifs();

        return $this->render('users/index.html.twig', [
            'users' => $users,
        ]);
    }

    // Route de compatibilité
    #[Route('/collaborateurs', name: 'app_collaborateurs')]
    public function collaborateurs(): Response
    {
        return $this->redirectToRoute('app_users');
    }

    #[Route('/users/{id}', name: 'app_user_detail')]
    public function userDetail(int $id): Response
    {
        $user = $this->userRepository->find($id);

        if (!$user) {
            throw $this->createNotFoundException('Utilisateur non trouvé');
        }

        $missions = $user->getMissions();
        $semainesTravail = $user->getSemainesTravail();

        // Statistiques des heures (adapter si nécessaire)
        $statsHeures = [
            'totalHeures' => array_sum(array_map(fn($st) => $st->getHeuresSaisies(), $semainesTravail->toArray())),
            'nombreSemaines' => $semainesTravail->count(),
        ];

        return $this->render('users/detail.html.twig', [
            'user' => $user,
            'missions' => $missions,
            'semainesTravail' => $semainesTravail,
            'statsHeures' => $statsHeures,
        ]);
    }

    // Route de compatibilité
    #[Route('/collaborateurs/{id}', name: 'app_collaborateur_detail')]
    public function collaborateurDetail(int $id): Response
    {
        return $this->redirectToRoute('app_user_detail', ['id' => $id]);
    }

    #[Route('/missions', name: 'app_missions')]
    public function missions(): Response
    {
        $missions = $this->missionRepository->findAll();

        return $this->render('missions/index.html.twig', [
            'missions' => $missions,
        ]);
    }

    #[Route('/missions/{id}', name: 'app_mission_detail')]
    public function missionDetail(int $id): Response
    {
        $mission = $this->missionRepository->find($id);

        if (!$mission) {
            throw $this->createNotFoundException('Mission non trouvée');
        }

        $segments = $this->segmentRepository->findByMission($mission);
        $primesTotales = $this->primeCalculator->calculPrimesTotalesMission($mission);

        return $this->render('missions/detail.html.twig', [
            'mission' => $mission,
            'segments' => $segments,
            'primesTotales' => $primesTotales,
        ]);
    }

    #[Route('/calendrier', name: 'app_calendrier')]
    public function calendrier(): Response
    {
        return $this->render('calendrier/index.html.twig');
    }

    #[Route('/heures', name: 'app_heures')]
    public function heures(): Response
    {
        $users = $this->userRepository->findActifs();
        $semainesAvecHS = $this->semaineTravailRepository->findWithHeuresSupplementaires();

        return $this->render('heures/index.html.twig', [
            'users' => $users,
            'semainesAvecHS' => $semainesAvecHS,
        ]);
    }


    #[Route('/primes', name: 'app_primes')]
    public function primes(): Response
    {
        $missions = $this->missionRepository->findAll();
        $bareme = $this->primeCalculator->getBareme();

        return $this->render('primes/index.html.twig', [
            'missions' => $missions,
            'bareme' => $bareme,
        ]);
    }
}

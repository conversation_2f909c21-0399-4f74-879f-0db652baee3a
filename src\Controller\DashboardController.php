<?php

namespace App\Controller;

use App\Repository\CollaborateurRepository;
use App\Repository\MissionRepository;
use App\Repository\SegmentRepository;
use App\Repository\SemaineTravailRepository;
use App\Service\PrimeCalculatorService;
use App\Service\HeureCalculatorService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

class DashboardController extends AbstractController
{
    public function __construct(
        private CollaborateurRepository $collaborateurRepository,
        private MissionRepository $missionRepository,
        private SegmentRepository $segmentRepository,
        private SemaineTravailRepository $semaineTravailRepository,
        private PrimeCalculatorService $primeCalculator,
        private HeureCalculatorService $heureCalculator
    ) {}

    #[Route('/', name: 'app_dashboard')]
    public function index(): Response
    {
        // Statistiques générales
        $statsCollaborateurs = $this->collaborateurRepository->getStatistiques();
        $statsMissions = $this->missionRepository->getStatistiques();
        
        // Missions en cours et prochaines
        $missionsEnCours = $this->missionRepository->findEnCours();
        $prochaines = $this->missionRepository->findProchaines(5);
        
        // Collaborateurs actifs
        $collaborateursActifs = $this->collaborateurRepository->findWithActiveMissions();

        return $this->render('dashboard/index.html.twig', [
            'statsCollaborateurs' => $statsCollaborateurs,
            'statsMissions' => $statsMissions,
            'missionsEnCours' => $missionsEnCours,
            'prochaines' => $prochaines,
            'collaborateursActifs' => $collaborateursActifs,
        ]);
    }

    #[Route('/collaborateurs', name: 'app_collaborateurs')]
    public function collaborateurs(): Response
    {
        $collaborateurs = $this->collaborateurRepository->findAll();
        
        return $this->render('collaborateurs/index.html.twig', [
            'collaborateurs' => $collaborateurs,
        ]);
    }

    #[Route('/collaborateurs/{id}', name: 'app_collaborateur_detail')]
    public function collaborateurDetail(int $id): Response
    {
        $collaborateur = $this->collaborateurRepository->find($id);
        
        if (!$collaborateur) {
            throw $this->createNotFoundException('Collaborateur non trouvé');
        }

        $missions = $this->missionRepository->findByCollaborateur($collaborateur);
        $semainesTravail = $this->semaineTravailRepository->findByCollaborateur($collaborateur);
        
        // Statistiques des heures
        $statsHeures = $this->heureCalculator->getStatistiquesHeures($collaborateur, $semainesTravail);

        return $this->render('collaborateurs/detail.html.twig', [
            'collaborateur' => $collaborateur,
            'missions' => $missions,
            'semainesTravail' => $semainesTravail,
            'statsHeures' => $statsHeures,
        ]);
    }

    #[Route('/missions', name: 'app_missions')]
    public function missions(): Response
    {
        $missions = $this->missionRepository->findAll();
        
        return $this->render('missions/index.html.twig', [
            'missions' => $missions,
        ]);
    }

    #[Route('/missions/{id}', name: 'app_mission_detail')]
    public function missionDetail(int $id): Response
    {
        $mission = $this->missionRepository->find($id);
        
        if (!$mission) {
            throw $this->createNotFoundException('Mission non trouvée');
        }

        $segments = $this->segmentRepository->findByMission($mission);
        $primesTotales = $this->primeCalculator->calculPrimesTotalesMission($mission);

        return $this->render('missions/detail.html.twig', [
            'mission' => $mission,
            'segments' => $segments,
            'primesTotales' => $primesTotales,
        ]);
    }

    #[Route('/calendrier', name: 'app_calendrier')]
    public function calendrier(): Response
    {
        return $this->render('calendrier/index.html.twig');
    }

    #[Route('/heures', name: 'app_heures')]
    public function heures(): Response
    {
        $collaborateurs = $this->collaborateurRepository->findAll();
        $semainesAvecHS = $this->semaineTravailRepository->findWithHeuresSupplementaires();
        
        return $this->render('heures/index.html.twig', [
            'collaborateurs' => $collaborateurs,
            'semainesAvecHS' => $semainesAvecHS,
        ]);
    }

    #[Route('/primes', name: 'app_primes')]
    public function primes(): Response
    {
        $missions = $this->missionRepository->findAll();
        $bareme = $this->primeCalculator->getBareme();
        
        return $this->render('primes/index.html.twig', [
            'missions' => $missions,
            'bareme' => $bareme,
        ]);
    }
}

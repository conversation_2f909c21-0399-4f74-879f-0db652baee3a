{% extends 'base.html.twig' %}

{% block title %}Missions - OSI Manager{% endblock %}

{% block body %}
<div class="px-4 py-6 sm:px-0">
    <!-- En-tête -->
    <div class="sm:flex sm:items-center mb-8">
        <div class="sm:flex-auto">
            <h1 class="text-3xl font-bold text-gray-900">Missions</h1>
            <p class="mt-2 text-gray-600">Gestion des missions et déplacements</p>
        </div>
        <div class="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
            <button type="button" onclick="openModal('addMissionModal')" class="inline-flex items-center justify-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 sm:w-auto">
                Nouvelle mission
            </button>
        </div>
    </div>

    <!-- Filtres -->
    <div class="bg-white shadow rounded-lg mb-6">
        <div class="px-4 py-5 sm:p-6">
            <div class="grid grid-cols-1 gap-4 sm:grid-cols-4">
                <div>
                    <label for="searchMission" class="block text-sm font-medium text-gray-700">Rechercher</label>
                    <input type="text" id="searchMission" placeholder="Titre ou pays..." class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                </div>
                <div>
                    <label for="zoneFilter" class="block text-sm font-medium text-gray-700">Zone</label>
                    <select id="zoneFilter" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                        <option value="">Toutes les zones</option>
                        <option value="EURO">Zone Euro</option>
                        <option value="HORS_EURO">Hors Zone Euro</option>
                    </select>
                </div>
                <div>
                    <label for="niveauFilter" class="block text-sm font-medium text-gray-700">Niveau</label>
                    <select id="niveauFilter" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                        <option value="">Tous les niveaux</option>
                        <option value="1">Niveau 1</option>
                        <option value="2">Niveau 2</option>
                    </select>
                </div>
                <div>
                    <label for="statutFilter" class="block text-sm font-medium text-gray-700">Statut</label>
                    <select id="statutFilter" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                        <option value="">Tous</option>
                        <option value="en_cours">En cours</option>
                        <option value="prochaines">Prochaines</option>
                        <option value="terminees">Terminées</option>
                    </select>
                </div>
            </div>
        </div>
    </div>

    <!-- Liste des missions -->
    <div class="bg-white shadow overflow-hidden sm:rounded-md">
        <ul role="list" class="divide-y divide-gray-200">
            {% for mission in missions %}
                <li>
                    <div class="px-4 py-4">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <div class="h-10 w-10 rounded-full bg-purple-500 flex items-center justify-center">
                                        <span class="text-sm font-medium text-white">🌍</span>
                                    </div>
                                </div>
                                <div class="ml-4">
                                    <div class="flex items-center">
                                        <p class="text-sm font-medium text-gray-900">{{ mission.titre }}</p>
                                        <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                            {% if mission.zone == 'EURO' %}bg-blue-100 text-blue-800{% else %}bg-green-100 text-green-800{% endif %}">
                                            {{ mission.zone == 'EURO' ? 'Zone Euro' : 'Hors Zone Euro' }}
                                        </span>
                                        <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                            Niveau {{ mission.niveau }}
                                        </span>
                                    </div>
                                    <div class="flex items-center text-sm text-gray-500 mt-1">
                                        <p>
                                            {% if mission.users|length > 0 %}
                                                {% for user in mission.users %}
                                                    {{ user.nomComplet }}{% if not loop.last %}, {% endif %}
                                                {% endfor %}
                                            {% elseif mission.collaborateur %}
                                                {{ mission.collaborateur.nomComplet }}
                                            {% else %}
                                                Aucun utilisateur assigné
                                            {% endif %}
                                        </p>
                                        <span class="mx-2">•</span>
                                        <p>{{ mission.pays }}</p>
                                        <span class="mx-2">•</span>
                                        <p>{{ mission.dateDebut|date('d/m/Y') }} - {{ mission.dateFin|date('d/m/Y') }}</p>
                                        <span class="mx-2">•</span>
                                        <p>{{ mission.dureeJours }} jour(s)</p>
                                    </div>
                                </div>
                            </div>
                            <div class="flex items-center space-x-2">
                                {% set today = "now"|date("Y-m-d") %}
                                {% set dateDebut = mission.dateDebut|date("Y-m-d") %}
                                {% set dateFin = mission.dateFin|date("Y-m-d") %}

                                {% if dateDebut <= today and dateFin >= today %}
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        En cours
                                    </span>
                                {% elseif dateDebut > today %}
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                        Prochaine
                                    </span>
                                {% else %}
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                        Terminée
                                    </span>
                                {% endif %}

                                <div class="flex space-x-2">
                                    <a href="{{ path('app_mission_detail', {id: mission.id}) }}" class="text-blue-600 hover:text-blue-900 text-sm font-medium">
                                        Voir
                                    </a>
                                    <button type="button" onclick="editMission({{ mission.id }})" class="text-indigo-600 hover:text-indigo-900 text-sm font-medium">
                                        Modifier
                                    </button>
                                    <button type="button" onclick="deleteMission({{ mission.id }}, '{{ mission.titre }}')" class="text-red-600 hover:text-red-900 text-sm font-medium">
                                        Supprimer
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </li>
            {% else %}
                <li class="px-4 py-8 text-center text-gray-500">
                    Aucune mission trouvée
                </li>
            {% endfor %}
        </ul>
    </div>
</div>

<!-- Modal d'ajout de mission -->
<div id="addMissionModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Nouvelle mission</h3>
            <form id="addMissionForm">
                <div class="space-y-4">
                    <div>
                        <label for="userSelect" class="block text-sm font-medium text-gray-700">Utilisateurs *</label>
                        <div class="mt-1">
                            <input type="text" id="userSearch" placeholder="Rechercher des utilisateurs..." class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm mb-2">
                            <div id="userSelectContainer" class="border border-gray-300 rounded-md max-h-40 overflow-y-auto bg-white">
                                <div id="usersList" class="p-2">
                                    <!-- Les utilisateurs seront chargés ici -->
                                </div>
                            </div>
                            <div id="selectedUsers" class="mt-2 flex flex-wrap gap-2">
                                <!-- Les utilisateurs sélectionnés apparaîtront ici -->
                            </div>
                        </div>
                        <input type="hidden" id="selectedUserIds" name="userIds" required>
                    </div>
                    <div>
                        <label for="titreMission" class="block text-sm font-medium text-gray-700">Titre</label>
                        <input type="text" id="titreMission" name="titre" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                    </div>
                    <div>
                        <label for="paysMission" class="block text-sm font-medium text-gray-700">Pays</label>
                        <input type="text" id="paysMission" name="pays" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                    </div>
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label for="dateDebutMission" class="block text-sm font-medium text-gray-700">Date début</label>
                            <input type="date" id="dateDebutMission" name="dateDebut" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                        </div>
                        <div>
                            <label for="dateFinMission" class="block text-sm font-medium text-gray-700">Date fin</label>
                            <input type="date" id="dateFinMission" name="dateFin" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                        </div>
                    </div>
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label for="niveauMission" class="block text-sm font-medium text-gray-700">Niveau</label>
                            <select id="niveauMission" name="niveau" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                                <option value="">Sélectionner</option>
                                <option value="1">Niveau 1</option>
                                <option value="2">Niveau 2</option>
                            </select>
                        </div>
                        <div>
                            <label for="zoneMission" class="block text-sm font-medium text-gray-700">Zone</label>
                            <select id="zoneMission" name="zone" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                                <option value="">Sélectionner</option>
                                <option value="EURO">Zone Euro</option>
                                <option value="HORS_EURO">Hors Zone Euro</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="flex justify-end space-x-3 mt-6">
                    <button type="button" onclick="closeModal('addMissionModal')" class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200">
                        Annuler
                    </button>
                    <button type="submit" class="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700">
                        Créer
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal d'édition de mission -->
<div id="editMissionModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">Modifier la mission</h3>
                <button type="button" onclick="closeEditMissionModal()" class="text-gray-400 hover:text-gray-600">
                    <span class="sr-only">Fermer</span>
                    <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>

            <form id="editMissionForm">
                <input type="hidden" id="editMissionId" name="missionId">

                <div class="grid grid-cols-1 gap-4">
                    <div>
                        <label for="editTitre" class="block text-sm font-medium text-gray-700">Titre *</label>
                        <input type="text" id="editTitre" name="titre" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                    </div>

                    <div>
                        <label for="editPays" class="block text-sm font-medium text-gray-700">Pays *</label>
                        <input type="text" id="editPays" name="pays" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                    </div>

                    <div>
                        <label for="editDateDebut" class="block text-sm font-medium text-gray-700">Date de début *</label>
                        <input type="date" id="editDateDebut" name="dateDebut" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                    </div>

                    <div>
                        <label for="editDateFin" class="block text-sm font-medium text-gray-700">Date de fin *</label>
                        <input type="date" id="editDateFin" name="dateFin" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                    </div>

                    <div>
                        <label for="editNiveau" class="block text-sm font-medium text-gray-700">Niveau *</label>
                        <select id="editNiveau" name="niveau" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                            <option value="">Sélectionner un niveau</option>
                            <option value="1">Niveau 1</option>
                            <option value="2">Niveau 2</option>
                            <option value="3">Niveau 3</option>
                        </select>
                    </div>

                    <div>
                        <label for="editZone" class="block text-sm font-medium text-gray-700">Zone *</label>
                        <select id="editZone" name="zone" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                            <option value="">Sélectionner une zone</option>
                            <option value="EURO">Zone Euro</option>
                            <option value="HORS_EURO">Hors Zone Euro</option>
                        </select>
                    </div>

                    <div>
                        <label for="editUserSelect" class="block text-sm font-medium text-gray-700">Utilisateurs *</label>
                        <div class="mt-1">
                            <input type="text" id="editUserSearch" placeholder="Rechercher des utilisateurs..." class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm mb-2">
                            <div id="editUserSelectContainer" class="border border-gray-300 rounded-md max-h-40 overflow-y-auto bg-white">
                                <div id="editUsersList" class="p-2">
                                    <!-- Les utilisateurs seront chargés ici -->
                                </div>
                            </div>
                            <div id="editSelectedUsers" class="mt-2 flex flex-wrap gap-2">
                                <!-- Les utilisateurs sélectionnés apparaîtront ici -->
                            </div>
                        </div>
                        <input type="hidden" id="editSelectedUserIds" name="userIds" required>
                    </div>
                </div>

                <div class="mt-6 flex justify-end space-x-3">
                    <button type="button" onclick="closeEditMissionModal()" class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50">
                        Annuler
                    </button>
                    <button type="submit" class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700">
                        Modifier
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Variables globales pour la gestion des utilisateurs
let allUsers = [];
let selectedUserIds = [];
let editSelectedUserIds = [];

// Fonctions pour les modals
function openModal(modalId) {
    document.getElementById(modalId).classList.remove('hidden');
}

function closeModal(modalId) {
    document.getElementById(modalId).classList.add('hidden');
}

function closeEditMissionModal() {
    document.getElementById('editMissionModal').classList.add('hidden');
    document.getElementById('editMissionForm').reset();
    editSelectedUserIds = [];
    updateEditSelectedUsers();
}

// Gestion du sélecteur d'utilisateurs pour création
function renderUsers(users, containerId, searchId, isEdit = false) {
    const container = document.getElementById(containerId);
    const searchTerm = document.getElementById(searchId).value.toLowerCase();

    container.innerHTML = '';

    const filteredUsers = users.filter(user =>
        user.nom.toLowerCase().includes(searchTerm) ||
        user.prenom.toLowerCase().includes(searchTerm) ||
        user.role.toLowerCase().includes(searchTerm)
    );

    filteredUsers.forEach(user => {
        const userDiv = document.createElement('div');
        userDiv.className = 'flex items-center p-2 hover:bg-gray-100 cursor-pointer';

        const currentSelectedIds = isEdit ? editSelectedUserIds : selectedUserIds;
        const isSelected = currentSelectedIds.includes(user.id);

        userDiv.innerHTML = `
            <input type="checkbox" ${isSelected ? 'checked' : ''} class="mr-2" onchange="${isEdit ? 'toggleEditUser' : 'toggleUser'}(${user.id})">
            <span class="text-sm">${user.prenom} ${user.nom} (${user.role})</span>
        `;

        container.appendChild(userDiv);
    });
}

function toggleUser(userId) {
    const index = selectedUserIds.indexOf(userId);
    if (index > -1) {
        selectedUserIds.splice(index, 1);
    } else {
        selectedUserIds.push(userId);
    }
    updateSelectedUsers();
    renderUsers(allUsers, 'usersList', 'userSearch');
}

function toggleEditUser(userId) {
    const index = editSelectedUserIds.indexOf(userId);
    if (index > -1) {
        editSelectedUserIds.splice(index, 1);
    } else {
        editSelectedUserIds.push(userId);
    }
    updateEditSelectedUsers();
    renderUsers(allUsers, 'editUsersList', 'editUserSearch', true);
}

function updateSelectedUsers() {
    const container = document.getElementById('selectedUsers');
    const hiddenInput = document.getElementById('selectedUserIds');

    container.innerHTML = '';
    hiddenInput.value = selectedUserIds.join(',');

    selectedUserIds.forEach(userId => {
        const user = allUsers.find(u => u.id === userId);
        if (user) {
            const tag = document.createElement('span');
            tag.className = 'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800';
            tag.innerHTML = `
                ${user.prenom} ${user.nom}
                <button type="button" onclick="toggleUser(${userId})" class="ml-1 text-blue-600 hover:text-blue-800">×</button>
            `;
            container.appendChild(tag);
        }
    });
}

function updateEditSelectedUsers() {
    const container = document.getElementById('editSelectedUsers');
    const hiddenInput = document.getElementById('editSelectedUserIds');

    container.innerHTML = '';
    hiddenInput.value = editSelectedUserIds.join(',');

    editSelectedUserIds.forEach(userId => {
        const user = allUsers.find(u => u.id === userId);
        if (user) {
            const tag = document.createElement('span');
            tag.className = 'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800';
            tag.innerHTML = `
                ${user.prenom} ${user.nom}
                <button type="button" onclick="toggleEditUser(${userId})" class="ml-1 text-blue-600 hover:text-blue-800">×</button>
            `;
            container.appendChild(tag);
        }
    });
}

// Charger les utilisateurs
async function loadUsers() {
    try {
        const response = await ajax.get("{{ path('api_user_index') }}");
        allUsers = response.data || response;
        renderUsers(allUsers, 'usersList', 'userSearch');
    } catch (error) {
        console.error('Erreur lors du chargement des utilisateurs:', error);
    }
}

async function loadUsersForEdit() {
    if (allUsers.length === 0) {
        await loadUsers();
    }
    renderUsers(allUsers, 'editUsersList', 'editUserSearch', true);
}

// Fonction pour éditer une mission
async function editMission(id) {
    try {
        const response = await ajax.get(`{{ path('api_mission_show', {id: '__ID__'}) }}`.replace('__ID__', id));
        const mission = response.data || response;

        document.getElementById('editMissionId').value = mission.id;
        document.getElementById('editTitre').value = mission.titre || '';
        document.getElementById('editPays').value = mission.pays || '';
        document.getElementById('editDateDebut').value = mission.dateDebut ? mission.dateDebut.split('T')[0] : '';
        document.getElementById('editDateFin').value = mission.dateFin ? mission.dateFin.split('T')[0] : '';
        document.getElementById('editNiveau').value = mission.niveau || '';
        document.getElementById('editZone').value = mission.zone || '';

        await loadUsersForEdit();
        editSelectedUserIds = mission.users ? mission.users.map(u => u.id) : [];
        updateEditSelectedUsers();

        document.getElementById('editMissionModal').classList.remove('hidden');
    } catch (error) {
        console.error('Erreur lors du chargement de la mission:', error);
        alert('Erreur lors du chargement des données de la mission');
    }
}

// Fonction pour supprimer une mission
async function deleteMission(id, titre) {
    if (!confirm(`Êtes-vous sûr de vouloir supprimer la mission "${titre}" ?\n\nCette action est irréversible.`)) {
        return;
    }

    try {
        const response = await ajax.delete(`{{ path('api_mission_delete', {id: '__ID__'}) }}`.replace('__ID__', id));
        if (response.status === 204) {
            location.reload();
        }
    } catch (error) {
        console.error('Erreur lors de la suppression de la mission:', error);
        alert('Erreur lors de la suppression de la mission.');
    }
}

// Event listeners
document.addEventListener('DOMContentLoaded', function() {
    loadUsers();

    // Recherche d'utilisateurs pour création
    document.getElementById('userSearch').addEventListener('input', function() {
        renderUsers(allUsers, 'usersList', 'userSearch');
    });

    // Recherche d'utilisateurs pour édition
    document.getElementById('editUserSearch').addEventListener('input', function() {
        renderUsers(allUsers, 'editUsersList', 'editUserSearch', true);
    });
});

// Gestion du formulaire d'ajout
document.getElementById('addMissionForm').addEventListener('submit', async function(e) {
    e.preventDefault();

    if (selectedUserIds.length === 0) {
        alert('Veuillez sélectionner au moins un utilisateur');
        return;
    }

    const formData = new FormData(e.target);
    const data = {
        userIds: selectedUserIds,
        titre: formData.get('titre'),
        pays: formData.get('pays'),
        dateDebut: formData.get('dateDebut'),
        dateFin: formData.get('dateFin'),
        niveau: parseInt(formData.get('niveau')),
        zone: formData.get('zone')
    };

    try {
        const response = await ajax.post("{{ path('api_mission_create') }}", data);
        if (response.status === 201) {
            location.reload();
        }
    } catch (error) {
        alert('Erreur lors de la création de la mission');
        console.error(error);
    }
});

// Gestion du formulaire d'édition
document.getElementById('editMissionForm').addEventListener('submit', async function(e) {
    e.preventDefault();

    if (editSelectedUserIds.length === 0) {
        alert('Veuillez sélectionner au moins un utilisateur');
        return;
    }

    const formData = new FormData(e.target);
    const missionId = formData.get('missionId');
    const data = {
        userIds: editSelectedUserIds,
        titre: formData.get('titre'),
        pays: formData.get('pays'),
        dateDebut: formData.get('dateDebut'),
        dateFin: formData.get('dateFin'),
        niveau: parseInt(formData.get('niveau')),
        zone: formData.get('zone')
    };

    try {
        const response = await ajax.put(`{{ path('api_mission_update', {id: '__ID__'}) }}`.replace('__ID__', missionId), data);
        if (response.status === 200) {
            closeEditMissionModal();
            location.reload();
        }
    } catch (error) {
        alert('Erreur lors de la modification de la mission');
        console.error(error);
    }
});
</script>
{% endblock %}

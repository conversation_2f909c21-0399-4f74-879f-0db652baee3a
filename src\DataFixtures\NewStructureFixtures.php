<?php

namespace App\DataFixtures;

use App\Entity\Client;
use App\Entity\Site;
use App\Entity\User;
use App\Entity\Mission;
use App\Entity\Segment;
use App\Entity\SemaineTravail;
use Doctrine\Bundle\FixturesBundle\Fixture;
use Doctrine\Persistence\ObjectManager;

class NewStructureFixtures extends Fixture
{
    public function load(ObjectManager $manager): void
    {
        // Créer des clients
        $clients = [];
        
        $client1 = new Client();
        $client1->setNom('ACME Corporation')
            ->setRaisonSociale('ACME Corp SAS')
            ->setSiret('12345678901234')
            ->setEmail('<EMAIL>')
            ->setTelephone('01.23.45.67.89')
            ->setAdresse('123 Rue de la Paix')
            ->setCodePostal('75001')
            ->setVille('Paris')
            ->setPays('France')
            ->setActif(true);
        $clients[] = $client1;
        $manager->persist($client1);

        $client2 = new Client();
        $client2->setNom('TechSolutions')
            ->setRaisonSociale('TechSolutions SARL')
            ->setSiret('98765432109876')
            ->setEmail('<EMAIL>')
            ->setTelephone('***********.90')
            ->setAdresse('456 Avenue des Champs')
            ->setCodePostal('69000')
            ->setVille('Lyon')
            ->setPays('France')
            ->setActif(true);
        $clients[] = $client2;
        $manager->persist($client2);

        // Créer des sites pour chaque client
        $sites = [];
        
        // Sites pour ACME
        $site1 = new Site();
        $site1->setClient($client1)
            ->setNom('Siège Social Paris')
            ->setAdresse('123 Rue de la Paix')
            ->setCodePostal('75001')
            ->setVille('Paris')
            ->setPays('France')
            ->setTelephone('01.23.45.67.89')
            ->setActif(true);
        $sites[] = $site1;
        $manager->persist($site1);

        $site2 = new Site();
        $site2->setClient($client1)
            ->setNom('Filiale Marseille')
            ->setAdresse('789 Boulevard du Prado')
            ->setCodePostal('13000')
            ->setVille('Marseille')
            ->setPays('France')
            ->setTelephone('***********.67')
            ->setActif(true);
        $sites[] = $site2;
        $manager->persist($site2);

        // Sites pour TechSolutions
        $site3 = new Site();
        $site3->setClient($client2)
            ->setNom('Bureau Principal Lyon')
            ->setAdresse('456 Avenue des Champs')
            ->setCodePostal('69000')
            ->setVille('Lyon')
            ->setPays('France')
            ->setTelephone('***********.90')
            ->setActif(true);
        $sites[] = $site3;
        $manager->persist($site3);

        // Créer des utilisateurs
        $users = [];
        
        $user1 = new User();
        $user1->setNom('Dupont')
            ->setPrenom('Jean')
            ->setEmail('<EMAIL>')
            ->setRole('Consultant')
            ->setHoraireHebdo(35)
            ->setForfaitJour(false)
            ->setTelephone('***********.78')
            ->setDateEmbauche(new \DateTime('-2 years'))
            ->setActif(true);
        $users[] = $user1;
        $manager->persist($user1);

        $user2 = new User();
        $user2->setNom('Martin')
            ->setPrenom('Marie')
            ->setEmail('<EMAIL>')
            ->setRole('Chef de projet')
            ->setHoraireHebdo(39)
            ->setForfaitJour(true)
            ->setTelephone('06.23.45.67.89')
            ->setDateEmbauche(new \DateTime('-3 years'))
            ->setActif(true);
        $users[] = $user2;
        $manager->persist($user2);

        $user3 = new User();
        $user3->setNom('Bernard')
            ->setPrenom('Pierre')
            ->setEmail('<EMAIL>')
            ->setRole('Consultant Senior')
            ->setHoraireHebdo(35)
            ->setForfaitJour(false)
            ->setTelephone('06.34.56.78.90')
            ->setDateEmbauche(new \DateTime('-5 years'))
            ->setActif(true);
        $users[] = $user3;
        $manager->persist($user3);

        // Créer des missions
        $missions = [];
        
        $mission1 = new Mission();
        $mission1->setClient($client1)
            ->setSite($site1)
            ->setTitre('Migration système ACME')
            ->setPays('France')
            ->setDateDebut(new \DateTime('-1 month'))
            ->setDateFin(new \DateTime('+2 months'))
            ->setNiveau(Mission::NIVEAU_2)
            ->setZone(Mission::ZONE_EURO);
        $mission1->addUser($user1);
        $mission1->addUser($user2);
        $missions[] = $mission1;
        $manager->persist($mission1);

        $mission2 = new Mission();
        $mission2->setClient($client2)
            ->setSite($site3)
            ->setTitre('Audit sécurité TechSolutions')
            ->setPays('France')
            ->setDateDebut(new \DateTime('-2 weeks'))
            ->setDateFin(new \DateTime('+1 month'))
            ->setNiveau(Mission::NIVEAU_1)
            ->setZone(Mission::ZONE_EURO);
        $mission2->addUser($user2);
        $mission2->addUser($user3);
        $missions[] = $mission2;
        $manager->persist($mission2);

        // Créer des segments
        $segments = [];
        
        // Segments pour user1 sur mission1
        $segment1 = new Segment();
        $segment1->setMission($mission1)
            ->setUser($user1)
            ->setType('INTERVENTION')
            ->setDateHeureDebut(new \DateTime('2024-01-15 09:00'))
            ->setDateHeureFin(new \DateTime('2024-01-15 17:30'));
        $segments[] = $segment1;
        $manager->persist($segment1);

        $segment2 = new Segment();
        $segment2->setMission($mission1)
            ->setUser($user1)
            ->setType('INTERVENTION')
            ->setDateHeureDebut(new \DateTime('2024-01-16 08:30'))
            ->setDateHeureFin(new \DateTime('2024-01-16 18:00'));
        $segments[] = $segment2;
        $manager->persist($segment2);

        // Segments pour user2 sur mission2
        $segment3 = new Segment();
        $segment3->setMission($mission2)
            ->setUser($user2)
            ->setType('AUDIT')
            ->setDateHeureDebut(new \DateTime('2024-01-17 10:00'))
            ->setDateHeureFin(new \DateTime('2024-01-17 16:00'));
        $segments[] = $segment3;
        $manager->persist($segment3);

        // Créer quelques semaines de travail
        $semaine1 = new SemaineTravail();
        $semaine1->setUser($user1)
            ->setSemaineAnnee('2024-03')
            ->setHeuresSaisies(37.5)
            ->setSource('manuelle');
        $manager->persist($semaine1);

        $semaine2 = new SemaineTravail();
        $semaine2->setUser($user2)
            ->setSemaineAnnee('2024-03')
            ->setHeuresSaisies(42.0)
            ->setSource('automatique');
        $manager->persist($semaine2);

        $manager->flush();
    }
}

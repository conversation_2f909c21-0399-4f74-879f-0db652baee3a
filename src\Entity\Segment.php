<?php

namespace App\Entity;


use App\Repository\SegmentRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Serializer\Annotation\Groups;

#[ORM\Entity(repositoryClass: SegmentRepository::class)]

class Segment
{
    public const TYPE_VOYAGE = 'VOYAGE';
    public const TYPE_INTERVENTION = 'INTERVENTION';
    public const TYPE_STAND_BY = 'STAND_BY';

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    #[Groups(['segment:read'])]
    private ?int $id = null;

    #[ORM\ManyToOne(inversedBy: 'segments')]
    #[ORM\JoinColumn(nullable: false)]
    #[Groups(['segment:read', 'segment:write'])]
    private ?Mission $mission = null;

    #[ORM\ManyToOne(inversedBy: 'segments')]
    #[ORM\JoinColumn(nullable: false)]
    #[Groups(['segment:read', 'segment:write'])]
    private ?User $user = null;

    #[ORM\Column(length: 20)]
    #[Assert\NotBlank]
    #[Assert\Choice(choices: [self::TYPE_VOYAGE, self::TYPE_INTERVENTION, self::TYPE_STAND_BY])]
    #[Groups(['segment:read', 'segment:write'])]
    private ?string $type = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE)]
    #[Assert\NotNull]
    #[Groups(['segment:read', 'segment:write'])]
    private ?\DateTimeInterface $dateHeureDebut = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE)]
    #[Assert\NotNull]
    #[Groups(['segment:read', 'segment:write'])]
    private ?\DateTimeInterface $dateHeureFin = null;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getMission(): ?Mission
    {
        return $this->mission;
    }

    public function setMission(?Mission $mission): static
    {
        $this->mission = $mission;
        return $this;
    }

    public function getType(): ?string
    {
        return $this->type;
    }

    public function setType(string $type): static
    {
        $this->type = $type;
        return $this;
    }

    public function getDateHeureDebut(): ?\DateTimeInterface
    {
        return $this->dateHeureDebut;
    }

    public function setDateHeureDebut(\DateTimeInterface $dateHeureDebut): static
    {
        $this->dateHeureDebut = $dateHeureDebut;
        return $this;
    }

    public function getDateHeureFin(): ?\DateTimeInterface
    {
        return $this->dateHeureFin;
    }

    public function setDateHeureFin(\DateTimeInterface $dateHeureFin): static
    {
        $this->dateHeureFin = $dateHeureFin;
        return $this;
    }

    public function getDureeHeures(): float
    {
        if (!$this->dateHeureDebut || !$this->dateHeureFin) {
            return 0;
        }
        return ($this->dateHeureFin->getTimestamp() - $this->dateHeureDebut->getTimestamp()) / 3600;
    }

    public function getDureeMinutes(): int
    {
        if (!$this->dateHeureDebut || !$this->dateHeureFin) {
            return 0;
        }
        return (int) (($this->dateHeureFin->getTimestamp() - $this->dateHeureDebut->getTimestamp()) / 60);
    }

    public function isVoyage(): bool
    {
        return $this->type === self::TYPE_VOYAGE;
    }

    public function isIntervention(): bool
    {
        return $this->type === self::TYPE_INTERVENTION;
    }

    public function isStandBy(): bool
    {
        return $this->type === self::TYPE_STAND_BY;
    }

    public function getJour(): \DateTimeInterface
    {
        return $this->dateHeureDebut ?
            (clone $this->dateHeureDebut)->setTime(0, 0, 0) :
            new \DateTime();
    }

    public function isWeekend(): bool
    {
        if (!$this->dateHeureDebut) {
            return false;
        }
        $dayOfWeek = (int) $this->dateHeureDebut->format('N');
        return $dayOfWeek >= 6; // 6 = samedi, 7 = dimanche
    }

    public function isHorsPlagesNormales(): bool
    {
        if (!$this->dateHeureDebut || !$this->dateHeureFin) {
            return false;
        }

        $heureDebut = (int) $this->dateHeureDebut->format('H');
        $heureFin = (int) $this->dateHeureFin->format('H');

        // Plages normales : 09:00-12:00 et 14:00-18:00
        $dansPlageMatinale = ($heureDebut >= 9 && $heureDebut < 12);
        $dansPlageAprem = ($heureDebut >= 14 && $heureDebut < 18);

        return !($dansPlageMatinale || $dansPlageAprem);
    }

    /**
     * Validation personnalisée : la date de fin doit être après la date de début
     */
    #[Assert\Callback]
    public function validate(\Symfony\Component\Validator\Context\ExecutionContextInterface $context): void
    {
        if ($this->dateHeureDebut && $this->dateHeureFin && $this->dateHeureFin <= $this->dateHeureDebut) {
            $context->buildViolation('La date de fin doit être postérieure à la date de début.')
                ->atPath('dateHeureFin')
                ->addViolation();
        }
    }

    public function getUser(): ?User
    {
        return $this->user;
    }

    public function setUser(?User $user): static
    {
        $this->user = $user;
        return $this;
    }

    public function __toString(): string
    {
        return sprintf(
            '%s - %s (%s)',
            $this->type,
            $this->dateHeureDebut?->format('d/m/Y H:i'),
            $this->mission?->getTitre() ?? 'Mission inconnue'
        );
    }
}

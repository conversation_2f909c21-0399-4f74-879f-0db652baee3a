{% extends 'base.html.twig' %}

{% block title %}Gestion des heures - OSI Manager{% endblock %}

{% block body %}
<div class="px-4 py-6 sm:px-0">
    <!-- En-tête -->
    <div class="sm:flex sm:items-center mb-8">
        <div class="sm:flex-auto">
            <h1 class="text-3xl font-bold text-gray-900">Gestion des heures</h1>
            <p class="mt-2 text-gray-600">Suivi du temps de travail et heures supplémentaires</p>
        </div>
        <div class="mt-4 sm:mt-0 sm:ml-16 sm:flex-none space-x-3">
            <button type="button" onclick="openModal('addHeuresModal')" class="inline-flex items-center justify-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 sm:w-auto">
                Saisir les heures
            </button>
            <button type="button" onclick="openModal('syncHeuresModal')" class="inline-flex items-center justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 sm:w-auto">
                🔄 Synchroniser depuis les missions
            </button>
        </div>
    </div>

    <!-- Filtres et sélection -->
    <div class="bg-white shadow rounded-lg mb-6">
        <div class="px-4 py-5 sm:p-6">
            <div class="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5">
                <div>
                    <label for="collaborateurFilter" class="block text-sm font-medium text-gray-700">Collaborateur</label>
                    <select id="collaborateurFilter" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                        <option value="">Tous les collaborateurs</option>
                        {% for collaborateur in collaborateurs %}
                            <option value="{{ collaborateur.id }}">{{ collaborateur.nomComplet }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div>
                    <label for="semaineDebut" class="block text-sm font-medium text-gray-700">Semaine début</label>
                    <input type="week" id="semaineDebut" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                </div>
                <div>
                    <label for="semaineFin" class="block text-sm font-medium text-gray-700">Semaine fin</label>
                    <input type="week" id="semaineFin" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                </div>
                <div>
                    <label for="typeContratFilter" class="block text-sm font-medium text-gray-700">Type de contrat</label>
                    <select id="typeContratFilter" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                        <option value="">Tous les types</option>
                        <option value="forfait">Forfait jour</option>
                        <option value="horaire">Horaire</option>
                    </select>
                </div>
                <div>
                    <label for="seuilHSFilter" class="block text-sm font-medium text-gray-700">Seuil H.S.</label>
                    <select id="seuilHSFilter" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                        <option value="">Toutes les semaines</option>
                        <option value="avec">Avec H.S. uniquement</option>
                        <option value="sans">Sans H.S. uniquement</option>
                    </select>
                </div>
            </div>
            <div class="mt-4 flex flex-wrap gap-2">
                <button type="button" onclick="loadHeuresData(1)" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700">
                    Filtrer
                </button>
                <button type="button" onclick="resetFilters()" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50">
                    Réinitialiser
                </button>
                <button type="button" onclick="exportData()" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50">
                    📊 Exporter
                </button>
            </div>
        </div>
    </div>

    <!-- Résumé des heures supplémentaires -->
    <div class="grid grid-cols-1 gap-5 sm:grid-cols-3 mb-8">
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-orange-500 rounded-md flex items-center justify-center">
                            <span class="text-white text-sm font-medium">⏰</span>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Semaines avec H.S.</dt>
                            <dd class="text-lg font-medium text-gray-900" id="semainesHS">{{ semainesAvecHS|length }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-red-500 rounded-md flex items-center justify-center">
                            <span class="text-white text-sm font-medium">📈</span>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Total H.S.</dt>
                            <dd class="text-lg font-medium text-gray-900" id="totalHS">-</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                            <span class="text-white text-sm font-medium">📊</span>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Moyenne/semaine</dt>
                            <dd class="text-lg font-medium text-gray-900" id="moyenneHS">-</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Tableau des heures -->
    <div class="bg-white shadow overflow-hidden sm:rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Détail des heures</h3>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200" id="heuresTable">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Collaborateur</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Semaine</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Heures saisies</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Heures normales</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">H.S. x1.25</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">H.S. x1.50</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Source</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200" id="heuresTableBody">
                        <!-- Les données seront chargées dynamiquement -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Modal de saisie des heures -->
<div id="addHeuresModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Saisir les heures</h3>
            <form id="addHeuresForm">
                <div class="space-y-4">
                    <div>
                        <label for="collaborateurHeures" class="block text-sm font-medium text-gray-700">Collaborateur</label>
                        <select id="collaborateurHeures" name="collaborateurId" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                            <option value="">Sélectionner un collaborateur</option>
                            {% for collaborateur in collaborateurs %}
                                <option value="{{ collaborateur.id }}">{{ collaborateur.nomComplet }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div>
                        <label for="semaineAnnee" class="block text-sm font-medium text-gray-700">Semaine</label>
                        <input type="week" id="semaineAnnee" name="semaineAnnee" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                    </div>
                    <div>
                        <label for="heuresSaisies" class="block text-sm font-medium text-gray-700">Heures travaillées</label>
                        <input type="number" id="heuresSaisies" name="heuresSaisies" min="0" max="168" step="0.5" required
                               class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                               oninput="validateHeures(this)">
                        <div id="heuresValidation" class="mt-2 text-sm"></div>
                        <div id="heuresPreview" class="mt-2 p-3 bg-gray-50 rounded-md hidden">
                            <h4 class="font-medium text-gray-900 mb-2">Aperçu du calcul :</h4>
                            <div class="space-y-1 text-sm text-gray-600">
                                <div>Heures normales : <span id="previewNormales">-</span>h</div>
                                <div>H.S. x1.25 : <span id="previewHS25">-</span>h</div>
                                <div>H.S. x1.50 : <span id="previewHS50">-</span>h</div>
                                <div class="font-medium text-gray-900">Total H.S. : <span id="previewTotalHS">-</span>h</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="flex justify-end space-x-3 mt-6">
                    <button type="button" onclick="closeModal('addHeuresModal')" class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200">
                        Annuler
                    </button>
                    <button type="submit" class="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700">
                        Enregistrer
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal de synchronisation des heures -->
<div id="syncHeuresModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
    <div class="relative top-20 mx-auto p-5 border w-4/5 max-w-4xl shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Synchroniser les heures depuis les missions</h3>

            <!-- Onglets -->
            <div class="border-b border-gray-200 mb-4">
                <nav class="-mb-px flex space-x-8">
                    <button onclick="switchSyncTab('collaborateur')" id="tabCollaborateur" class="sync-tab-active border-blue-500 text-blue-600 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                        Par collaborateur
                    </button>
                    <button onclick="switchSyncTab('tous')" id="tabTous" class="sync-tab-inactive border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                        Tous les collaborateurs
                    </button>
                </nav>
            </div>

            <!-- Contenu onglet collaborateur -->
            <div id="syncCollaborateur" class="sync-tab-content">
                <div class="grid grid-cols-1 gap-4 sm:grid-cols-3 mb-4">
                    <div>
                        <label for="syncCollaborateurSelect" class="block text-sm font-medium text-gray-700">Collaborateur</label>
                        <select id="syncCollaborateurSelect" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                            <option value="">Sélectionner un collaborateur</option>
                            {% for collaborateur in collaborateurs %}
                                <option value="{{ collaborateur.id }}">{{ collaborateur.nomComplet }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div>
                        <label for="syncDateDebut" class="block text-sm font-medium text-gray-700">Date début</label>
                        <input type="date" id="syncDateDebut" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                    </div>
                    <div>
                        <label for="syncDateFin" class="block text-sm font-medium text-gray-700">Date fin</label>
                        <input type="date" id="syncDateFin" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                    </div>
                </div>

                <div class="flex space-x-3 mb-4">
                    <button type="button" onclick="previewSynchronisation()" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50">
                        👁️ Aperçu
                    </button>
                    <button type="button" onclick="executerSynchronisation()" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700">
                        🔄 Synchroniser
                    </button>
                </div>
            </div>

            <!-- Contenu onglet tous -->
            <div id="syncTous" class="sync-tab-content hidden">
                <div class="grid grid-cols-1 gap-4 sm:grid-cols-2 mb-4">
                    <div>
                        <label for="syncTousDateDebut" class="block text-sm font-medium text-gray-700">Date début</label>
                        <input type="date" id="syncTousDateDebut" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                    </div>
                    <div>
                        <label for="syncTousDateFin" class="block text-sm font-medium text-gray-700">Date fin</label>
                        <input type="date" id="syncTousDateFin" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                    </div>
                </div>

                <div class="flex space-x-3 mb-4">
                    <button type="button" onclick="previewSynchronisationTous()" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50">
                        👁️ Aperçu global
                    </button>
                    <button type="button" onclick="executerSynchronisationTous()" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700">
                        🔄 Synchroniser tous
                    </button>
                </div>
            </div>

            <!-- Zone de résultats -->
            <div id="syncResults" class="hidden">
                <div class="bg-gray-50 rounded-lg p-4 mb-4">
                    <h4 class="font-medium text-gray-900 mb-2">Résultats de la synchronisation</h4>
                    <div id="syncResultsContent"></div>
                </div>
            </div>

            <!-- Options avancées -->
            <div class="bg-yellow-50 border border-yellow-200 rounded-md p-4 mb-4">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <span class="text-yellow-400">⚠️</span>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-yellow-800">Options avancées</h3>
                        <div class="mt-2">
                            <label class="inline-flex items-center">
                                <input type="checkbox" id="forceOverwrite" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                <span class="ml-2 text-sm text-yellow-700">Écraser les heures existantes (même si différentes)</span>
                            </label>
                        </div>
                    </div>
                </div>
            </div>

            <div class="flex justify-end space-x-3">
                <button type="button" onclick="closeModal('syncHeuresModal')" class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200">
                    Fermer
                </button>
            </div>
        </div>
    </div>
</div>

<script>
function openModal(modalId) {
    document.getElementById(modalId).classList.remove('hidden');
}

function closeModal(modalId) {
    document.getElementById(modalId).classList.add('hidden');
}

let currentPage = 1;
const itemsPerPage = 20;

// Charger les données d'heures
async function loadHeuresData(page = 1) {
    const collaborateurId = document.getElementById('collaborateurFilter').value;
    const semaineDebut = document.getElementById('semaineDebut').value;
    const semaineFin = document.getElementById('semaineFin').value;
    const typeContrat = document.getElementById('typeContratFilter')?.value;
    const seuil = document.getElementById('seuilHSFilter')?.value;

    try {
        let url = "{{ path('api_semaine_travail_index') }}";
        const params = new URLSearchParams();

        if (collaborateurId) params.append('collaborateur', collaborateurId);
        if (semaineDebut) params.append('debut', semaineDebut.replace('-W', '-'));
        if (semaineFin) params.append('fin', semaineFin.replace('-W', '-'));
        if (typeContrat) params.append('typeContrat', typeContrat);
        if (seuil) params.append('seuil', seuil);
        params.append('page', page);
        params.append('limit', itemsPerPage);

        if (params.toString()) {
            url += '?' + params.toString();
        }

        const response = await ajax.get(url);

        if (response.data && response.data.data) {
            displayHeuresData(response.data.data);
            updatePagination(response.data.pagination);
            updateStatistiques(response.data.statistiques);
            currentPage = page;
        } else {
            console.error('Format de données inattendu:', response.data);
            displayHeuresData([]);
        }
    } catch (error) {
        console.error('Erreur lors du chargement des données:', error);
        const tbody = document.getElementById('heuresTableBody');
        tbody.innerHTML = '<tr><td colspan="7" class="px-6 py-4 text-center text-red-600">Erreur lors du chargement des données</td></tr>';
    }
}

function displayHeuresData(semaines) {
    const tbody = document.getElementById('heuresTableBody');
    tbody.innerHTML = '';

    semaines.forEach(semaine => {
        // Vérifier que les données sont valides
        if (!semaine || typeof semaine.heuresSaisies !== 'number') {
            console.warn('Données de semaine invalides:', semaine);
            return;
        }

        // Nom du collaborateur avec vérification
        let nomCollaborateur = 'N/A';
        if (semaine.collaborateur) {
            const nom = semaine.collaborateur.nom || '';
            const prenom = semaine.collaborateur.prenom || '';
            if (nom || prenom) {
                nomCollaborateur = `${prenom} ${nom}`.trim();
            }
        }

        // Icône et couleur selon la source
        const sourceInfo = getSourceInfo(semaine.source);

        const row = document.createElement('tr');
        row.innerHTML = `
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                ${nomCollaborateur}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${semaine.semaineAnnee || 'N/A'}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${semaine.heuresSaisies.toFixed(1)}h</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${semaine.heuresNormales.toFixed(1)}h</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${semaine.heuresSupp25.toFixed(1)}h</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${semaine.heuresSupp50.toFixed(1)}h</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${sourceInfo.class}">
                    ${sourceInfo.icon} ${sourceInfo.text}
                </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <button onclick="editSemaine(${semaine.id})" class="text-blue-600 hover:text-blue-900">Modifier</button>
            </td>
        `;
        tbody.appendChild(row);
    });
}

function getSourceInfo(source) {
    switch (source) {
        case 'automatique':
            return {
                icon: '🔄',
                text: 'Auto',
                class: 'bg-blue-100 text-blue-800'
            };
        case 'manuelle':
            return {
                icon: '✏️',
                text: 'Manuel',
                class: 'bg-green-100 text-green-800'
            };
        default:
            return {
                icon: '❓',
                text: 'Inconnu',
                class: 'bg-gray-100 text-gray-800'
            };
    }
}

function updateStatistiques(stats) {
    document.getElementById('totalHS').textContent = stats.totalHS.toFixed(1) + 'h';
    document.getElementById('moyenneHS').textContent = stats.moyenneHS.toFixed(1) + 'h';
    document.getElementById('semainesHS').textContent = stats.semainesAvecHS;
}

function updatePagination(pagination) {
    // Créer ou mettre à jour les contrôles de pagination
    let paginationDiv = document.getElementById('pagination');
    if (!paginationDiv) {
        paginationDiv = document.createElement('div');
        paginationDiv.id = 'pagination';
        paginationDiv.className = 'flex items-center justify-between border-t border-gray-200 bg-white px-4 py-3 sm:px-6 mt-4';
        document.querySelector('#heuresTable').parentNode.appendChild(paginationDiv);
    }

    paginationDiv.innerHTML = `
        <div class="flex flex-1 justify-between sm:hidden">
            <button onclick="loadHeuresData(${pagination.page - 1})"
                    ${pagination.page <= 1 ? 'disabled' : ''}
                    class="relative inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50">
                Précédent
            </button>
            <button onclick="loadHeuresData(${pagination.page + 1})"
                    ${pagination.page >= pagination.pages ? 'disabled' : ''}
                    class="relative ml-3 inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50">
                Suivant
            </button>
        </div>
        <div class="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
            <div>
                <p class="text-sm text-gray-700">
                    Affichage de <span class="font-medium">${((pagination.page - 1) * pagination.limit) + 1}</span>
                    à <span class="font-medium">${Math.min(pagination.page * pagination.limit, pagination.total)}</span>
                    sur <span class="font-medium">${pagination.total}</span> résultats
                </p>
            </div>
            <div>
                <nav class="isolate inline-flex -space-x-px rounded-md shadow-sm" aria-label="Pagination">
                    <button onclick="loadHeuresData(${pagination.page - 1})"
                            ${pagination.page <= 1 ? 'disabled' : ''}
                            class="relative inline-flex items-center rounded-l-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 disabled:opacity-50">
                        ‹
                    </button>
                    ${generatePageNumbers(pagination)}
                    <button onclick="loadHeuresData(${pagination.page + 1})"
                            ${pagination.page >= pagination.pages ? 'disabled' : ''}
                            class="relative inline-flex items-center rounded-r-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 disabled:opacity-50">
                        ›
                    </button>
                </nav>
            </div>
        </div>
    `;
}

function generatePageNumbers(pagination) {
    let pages = '';
    const maxVisible = 5;
    let start = Math.max(1, pagination.page - Math.floor(maxVisible / 2));
    let end = Math.min(pagination.pages, start + maxVisible - 1);

    if (end - start + 1 < maxVisible) {
        start = Math.max(1, end - maxVisible + 1);
    }

    for (let i = start; i <= end; i++) {
        const isActive = i === pagination.page;
        pages += `
            <button onclick="loadHeuresData(${i})"
                    class="relative inline-flex items-center px-4 py-2 text-sm font-semibold ${
                        isActive
                            ? 'bg-blue-600 text-white focus:z-20 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600'
                            : 'text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0'
                    }">
                ${i}
            </button>
        `;
    }
    return pages;
}

function editSemaine(id) {
    // TODO: Implémenter la modification
    alert('Modification de la semaine ' + id);
}

function resetFilters() {
    document.getElementById('collaborateurFilter').value = '';
    document.getElementById('semaineDebut').value = '';
    document.getElementById('semaineFin').value = '';
    document.getElementById('typeContratFilter').value = '';
    document.getElementById('seuilHSFilter').value = '';
    loadHeuresData(1);
}

function exportData() {
    const collaborateurId = document.getElementById('collaborateurFilter').value;
    const semaineDebut = document.getElementById('semaineDebut').value;
    const semaineFin = document.getElementById('semaineFin').value;
    const typeContrat = document.getElementById('typeContratFilter').value;
    const seuil = document.getElementById('seuilHSFilter').value;

    const params = new URLSearchParams();
    if (collaborateurId) params.append('collaborateur', collaborateurId);
    if (semaineDebut) params.append('debut', semaineDebut.replace('-W', '-'));
    if (semaineFin) params.append('fin', semaineFin.replace('-W', '-'));
    if (typeContrat) params.append('typeContrat', typeContrat);
    if (seuil) params.append('seuil', seuil);
    params.append('export', 'csv');

    const url = "{{ path('api_semaine_travail_index') }}" + '?' + params.toString();
    window.open(url, '_blank');
}

// Gestion du formulaire de saisie
document.getElementById('addHeuresForm').addEventListener('submit', async function(e) {
    e.preventDefault();

    const formData = new FormData(e.target);
    const semaineValue = formData.get('semaineAnnee');
    const semaineFormatted = semaineValue.replace('-W', '-');

    const data = {
        collaborateurId: parseInt(formData.get('collaborateurId')),
        semaineAnnee: semaineFormatted,
        heuresSaisies: parseFloat(formData.get('heuresSaisies'))
    };

    try {
        const response = await ajax.post("{{ path('api_semaine_travail_create') }}", data);
        if (response.status === 201) {
            closeModal('addHeuresModal');
            loadHeuresData();
        }
    } catch (error) {
        if (error.response && error.response.status === 409) {
            alert('Cette semaine existe déjà pour ce collaborateur');
        } else {
            alert('Erreur lors de l\'enregistrement des heures');
        }
        console.error(error);
    }
});

// Validation temps réel des heures
function validateHeures(input) {
    const heures = parseFloat(input.value) || 0;
    const collaborateurId = document.getElementById('collaborateurHeures').value;
    const validationDiv = document.getElementById('heuresValidation');
    const previewDiv = document.getElementById('heuresPreview');

    // Réinitialiser les messages
    validationDiv.innerHTML = '';
    input.classList.remove('border-red-500', 'border-green-500');

    // Validation de base
    if (heures < 0) {
        showValidationError(input, validationDiv, 'Les heures ne peuvent pas être négatives');
        previewDiv.classList.add('hidden');
        return;
    }

    if (heures > 168) {
        showValidationError(input, validationDiv, 'Il n\'y a que 168 heures dans une semaine');
        previewDiv.classList.add('hidden');
        return;
    }

    // Validation avancée si un collaborateur est sélectionné
    if (collaborateurId && heures > 0) {
        const collaborateur = getCollaborateurById(collaborateurId);
        if (collaborateur) {
            validateAndPreviewHeures(input, validationDiv, previewDiv, heures, collaborateur);
        }
    } else if (heures > 0) {
        // Aperçu avec horaire standard de 35h
        previewHeures(previewDiv, heures, 35);
        input.classList.add('border-green-500');
    } else {
        previewDiv.classList.add('hidden');
    }
}

function showValidationError(input, validationDiv, message) {
    input.classList.add('border-red-500');
    validationDiv.innerHTML = `<span class="text-red-600">⚠️ ${message}</span>`;
}

function validateAndPreviewHeures(input, validationDiv, previewDiv, heures, collaborateur) {
    const horaireHebdo = collaborateur.horaireHebdo || 35;

    // Validation spécifique
    if (heures > 80) {
        showValidationError(input, validationDiv, 'Attention : Plus de 80h par semaine peut être problématique');
    } else if (heures > 60) {
        validationDiv.innerHTML = '<span class="text-orange-600">⚠️ Attention : Beaucoup d\'heures supplémentaires</span>';
        input.classList.add('border-orange-500');
    } else {
        input.classList.add('border-green-500');
    }

    // Aperçu du calcul
    previewHeures(previewDiv, heures, horaireHebdo);

    // Message spécifique pour forfait jour
    if (collaborateur.forfaitJour) {
        const existingMessage = validationDiv.innerHTML;
        validationDiv.innerHTML = existingMessage + '<br><span class="text-blue-600">ℹ️ Collaborateur en forfait jour</span>';
    }
}

function previewHeures(previewDiv, heures, horaireHebdo) {
    const heuresNormales = Math.min(heures, horaireHebdo);
    const heuresSupp = Math.max(0, heures - horaireHebdo);
    const heuresSupp25 = Math.min(heuresSupp, 8);
    const heuresSupp50 = Math.max(0, heuresSupp - 8);

    document.getElementById('previewNormales').textContent = heuresNormales.toFixed(1);
    document.getElementById('previewHS25').textContent = heuresSupp25.toFixed(1);
    document.getElementById('previewHS50').textContent = heuresSupp50.toFixed(1);
    document.getElementById('previewTotalHS').textContent = (heuresSupp25 + heuresSupp50).toFixed(1);

    previewDiv.classList.remove('hidden');
}

function getCollaborateurById(id) {
    // Récupérer les données du collaborateur depuis le select
    const select = document.getElementById('collaborateurHeures');
    const option = select.querySelector(`option[value="${id}"]`);
    if (option) {
        // Pour une vraie application, on récupérerait les données via API
        // Ici on utilise des valeurs par défaut
        return {
            id: id,
            horaireHebdo: 35, // Valeur par défaut
            forfaitJour: false // Valeur par défaut
        };
    }
    return null;
}

// Gestion des onglets de synchronisation
function switchSyncTab(tab) {
    // Masquer tous les contenus
    document.querySelectorAll('.sync-tab-content').forEach(content => {
        content.classList.add('hidden');
    });

    // Réinitialiser tous les onglets
    document.querySelectorAll('[id^="tab"]').forEach(tabButton => {
        tabButton.className = 'sync-tab-inactive border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm';
    });

    // Activer l'onglet sélectionné
    document.getElementById('tab' + tab.charAt(0).toUpperCase() + tab.slice(1)).className = 'sync-tab-active border-blue-500 text-blue-600 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm';
    document.getElementById('sync' + tab.charAt(0).toUpperCase() + tab.slice(1)).classList.remove('hidden');
}

// Aperçu de synchronisation pour un collaborateur
async function previewSynchronisation() {
    const collaborateurId = document.getElementById('syncCollaborateurSelect').value;
    const dateDebut = document.getElementById('syncDateDebut').value;
    const dateFin = document.getElementById('syncDateFin').value;

    if (!collaborateurId) {
        alert('Veuillez sélectionner un collaborateur');
        return;
    }

    try {
        let url = "{{ path('api_heures_sync_preview', {id: '__ID__'}) }}".replace('__ID__', collaborateurId);
        const params = new URLSearchParams();
        if (dateDebut) params.append('debut', dateDebut);
        if (dateFin) params.append('fin', dateFin);
        if (params.toString()) url += '?' + params.toString();

        const response = await ajax.get(url);
        displaySyncPreview(response.data);
    } catch (error) {
        console.error('Erreur lors de l\'aperçu:', error);
        alert('Erreur lors de l\'aperçu de synchronisation');
    }
}

// Exécuter la synchronisation pour un collaborateur
async function executerSynchronisation() {
    const collaborateurId = document.getElementById('syncCollaborateurSelect').value;
    const dateDebut = document.getElementById('syncDateDebut').value;
    const dateFin = document.getElementById('syncDateFin').value;
    const forceOverwrite = document.getElementById('forceOverwrite').checked;

    if (!collaborateurId) {
        alert('Veuillez sélectionner un collaborateur');
        return;
    }

    if (!confirm('Êtes-vous sûr de vouloir synchroniser les heures ? Cette action peut modifier les données existantes.')) {
        return;
    }

    try {
        const data = { forceOverwrite };
        if (dateDebut) data.debut = dateDebut;
        if (dateFin) data.fin = dateFin;

        const url = "{{ path('api_heures_sync_sync_collaborateur', {id: '__ID__'}) }}".replace('__ID__', collaborateurId);
        const response = await ajax.post(url, data);
        displaySyncResults(response.data);

        // Recharger les données de la page
        loadHeuresData(1);
    } catch (error) {
        console.error('Erreur lors de la synchronisation:', error);
        alert('Erreur lors de la synchronisation');
    }
}

// Aperçu de synchronisation pour tous les collaborateurs
async function previewSynchronisationTous() {
    const dateDebut = document.getElementById('syncTousDateDebut').value;
    const dateFin = document.getElementById('syncTousDateFin').value;

    try {
        let url = "{{ path('api_heures_sync_preview_tous') }}";
        const params = new URLSearchParams();
        if (dateDebut) params.append('debut', dateDebut);
        if (dateFin) params.append('fin', dateFin);
        if (params.toString()) url += '?' + params.toString();

        const response = await ajax.get(url);
        displaySyncPreviewTous(response.data);
    } catch (error) {
        console.error('Erreur lors de l\'aperçu global:', error);
        alert('Erreur lors de l\'aperçu global');
    }
}

// Exécuter la synchronisation pour tous les collaborateurs
async function executerSynchronisationTous() {
    const dateDebut = document.getElementById('syncTousDateDebut').value;
    const dateFin = document.getElementById('syncTousDateFin').value;
    const forceOverwrite = document.getElementById('forceOverwrite').checked;

    if (!confirm('ATTENTION: Vous allez synchroniser TOUS les collaborateurs. Cette action peut modifier de nombreuses données. Continuer ?')) {
        return;
    }

    try {
        const data = { forceOverwrite };
        if (dateDebut) data.debut = dateDebut;
        if (dateFin) data.fin = dateFin;

        const response = await ajax.post("{{ path('api_heures_sync_sync_tous') }}", data);
        displaySyncResults(response.data);

        // Recharger les données de la page
        loadHeuresData(1);
    } catch (error) {
        console.error('Erreur lors de la synchronisation globale:', error);
        alert('Erreur lors de la synchronisation globale');
    }
}

// Afficher l'aperçu de synchronisation
function displaySyncPreview(data) {
    const resultsDiv = document.getElementById('syncResults');
    const contentDiv = document.getElementById('syncResultsContent');

    let html = `
        <div class="mb-4">
            <h5 class="font-medium text-gray-900">Aperçu pour ${data.collaborateur}</h5>
            <div class="grid grid-cols-4 gap-4 mt-2 text-sm">
                <div class="bg-blue-100 p-2 rounded">
                    <div class="font-medium text-blue-800">${data.statistiques.totalSemaines}</div>
                    <div class="text-blue-600">Semaines</div>
                </div>
                <div class="bg-red-100 p-2 rounded">
                    <div class="font-medium text-red-800">${data.statistiques.semainesAvecConflits}</div>
                    <div class="text-red-600">Conflits</div>
                </div>
                <div class="bg-green-100 p-2 rounded">
                    <div class="font-medium text-green-800">${data.statistiques.totalHeuresCalculees.toFixed(1)}h</div>
                    <div class="text-green-600">H. calculées</div>
                </div>
                <div class="bg-gray-100 p-2 rounded">
                    <div class="font-medium text-gray-800">${data.statistiques.totalHeuresActuelles.toFixed(1)}h</div>
                    <div class="text-gray-600">H. actuelles</div>
                </div>
            </div>
        </div>
        <div class="max-h-64 overflow-y-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">Semaine</th>
                        <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">H. actuelles</th>
                        <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">H. calculées</th>
                        <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">Différence</th>
                        <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">Segments</th>
                        <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">Statut</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
    `;

    Object.entries(data.preview).forEach(([semaine, info]) => {
        const statusClass = info.conflit ? 'text-red-600' : (info.difference !== 0 ? 'text-orange-600' : 'text-green-600');
        const statusText = info.conflit ? 'Conflit' : (info.difference !== 0 ? 'Différence' : 'OK');

        html += `
            <tr>
                <td class="px-3 py-2 text-sm text-gray-900">${semaine}</td>
                <td class="px-3 py-2 text-sm text-gray-900">${info.heuresActuelles.toFixed(1)}h</td>
                <td class="px-3 py-2 text-sm text-gray-900">${info.heuresCalculees.toFixed(1)}h</td>
                <td class="px-3 py-2 text-sm text-gray-900">${info.difference > 0 ? '+' : ''}${info.difference.toFixed(1)}h</td>
                <td class="px-3 py-2 text-sm text-gray-900">${info.nombreSegments}</td>
                <td class="px-3 py-2 text-sm ${statusClass}">${statusText}</td>
            </tr>
        `;
    });

    html += '</tbody></table></div>';

    contentDiv.innerHTML = html;
    resultsDiv.classList.remove('hidden');
}

// Afficher l'aperçu global
function displaySyncPreviewTous(data) {
    const resultsDiv = document.getElementById('syncResults');
    const contentDiv = document.getElementById('syncResultsContent');

    let html = `
        <div class="mb-4">
            <h5 class="font-medium text-gray-900">Aperçu global</h5>
            <div class="grid grid-cols-4 gap-4 mt-2 text-sm">
                <div class="bg-blue-100 p-2 rounded">
                    <div class="font-medium text-blue-800">${data.statistiques.totalCollaborateurs}</div>
                    <div class="text-blue-600">Collaborateurs</div>
                </div>
                <div class="bg-green-100 p-2 rounded">
                    <div class="font-medium text-green-800">${data.statistiques.totalSemaines}</div>
                    <div class="text-green-600">Semaines</div>
                </div>
                <div class="bg-red-100 p-2 rounded">
                    <div class="font-medium text-red-800">${data.statistiques.totalConflits}</div>
                    <div class="text-red-600">Conflits</div>
                </div>
                <div class="bg-purple-100 p-2 rounded">
                    <div class="font-medium text-purple-800">${data.statistiques.totalHeuresCalculees.toFixed(1)}h</div>
                    <div class="text-purple-600">H. calculées</div>
                </div>
            </div>
        </div>
        <div class="max-h-64 overflow-y-auto">
            <div class="space-y-2">
    `;

    Object.entries(data.previews).forEach(([collaborateurId, info]) => {
        const conflits = info.statistiques.semainesAvecConflits;
        const statusClass = conflits > 0 ? 'border-red-200 bg-red-50' : 'border-green-200 bg-green-50';

        html += `
            <div class="border ${statusClass} rounded-lg p-3">
                <div class="flex justify-between items-center">
                    <span class="font-medium">${info.collaborateur}</span>
                    <div class="flex space-x-4 text-sm">
                        <span>${info.statistiques.totalSemaines} semaines</span>
                        <span class="${conflits > 0 ? 'text-red-600' : 'text-green-600'}">${conflits} conflits</span>
                        <span>${info.statistiques.totalHeuresCalculees.toFixed(1)}h calculées</span>
                    </div>
                </div>
            </div>
        `;
    });

    html += '</div></div>';

    contentDiv.innerHTML = html;
    resultsDiv.classList.remove('hidden');
}

// Afficher les résultats de synchronisation
function displaySyncResults(data) {
    const resultsDiv = document.getElementById('syncResults');
    const contentDiv = document.getElementById('syncResultsContent');

    if (data.success) {
        let html = `
            <div class="mb-4">
                <h5 class="font-medium text-green-900">✅ Synchronisation réussie</h5>
                ${data.collaborateur ? `<p class="text-sm text-gray-600">Collaborateur: ${data.collaborateur}</p>` : ''}
            </div>
        `;

        if (data.statistiques) {
            html += `
                <div class="grid grid-cols-4 gap-4 mb-4 text-sm">
                    <div class="bg-blue-100 p-2 rounded">
                        <div class="font-medium text-blue-800">${data.statistiques.creations || data.statistiques.totalCreations || 0}</div>
                        <div class="text-blue-600">Créations</div>
                    </div>
                    <div class="bg-green-100 p-2 rounded">
                        <div class="font-medium text-green-800">${data.statistiques.miseAJour || data.statistiques.totalMiseAJour || 0}</div>
                        <div class="text-green-600">Mises à jour</div>
                    </div>
                    <div class="bg-red-100 p-2 rounded">
                        <div class="font-medium text-red-800">${data.statistiques.conflits || data.statistiques.totalConflits || 0}</div>
                        <div class="text-red-600">Conflits</div>
                    </div>
                    <div class="bg-gray-100 p-2 rounded">
                        <div class="font-medium text-gray-800">${data.statistiques.totalSemaines || data.statistiques.collaborateursTraites || 0}</div>
                        <div class="text-gray-600">${data.statistiques.totalSemaines ? 'Semaines' : 'Collaborateurs'}</div>
                    </div>
                </div>
            `;
        }

        contentDiv.innerHTML = html;
    } else {
        contentDiv.innerHTML = `
            <div class="text-red-600">
                <h5 class="font-medium">❌ Erreur lors de la synchronisation</h5>
                <p class="text-sm mt-1">${data.error}</p>
            </div>
        `;
    }

    resultsDiv.classList.remove('hidden');
}

// Validation lors du changement de collaborateur
document.addEventListener('DOMContentLoaded', function() {
    const collaborateurSelect = document.getElementById('collaborateurHeures');
    const heuresInput = document.getElementById('heuresSaisies');

    collaborateurSelect.addEventListener('change', function() {
        if (heuresInput.value) {
            validateHeures(heuresInput);
        }
    });

    // Initialiser les dates par défaut pour la synchronisation
    const today = new Date();
    const threeMonthsAgo = new Date(today.getFullYear(), today.getMonth() - 3, today.getDate());
    const oneMonthLater = new Date(today.getFullYear(), today.getMonth() + 1, today.getDate());

    document.getElementById('syncDateDebut').value = threeMonthsAgo.toISOString().split('T')[0];
    document.getElementById('syncDateFin').value = oneMonthLater.toISOString().split('T')[0];
    document.getElementById('syncTousDateDebut').value = threeMonthsAgo.toISOString().split('T')[0];
    document.getElementById('syncTousDateFin').value = oneMonthLater.toISOString().split('T')[0];

    loadHeuresData();
});
</script>
{% endblock %}

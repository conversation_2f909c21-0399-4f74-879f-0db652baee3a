{% extends 'base.html.twig' %}

{% block title %}Gestion des heures - OSI Manager{% endblock %}

{% block body %}
<div class="px-4 py-6 sm:px-0">
    <!-- En-tête -->
    <div class="sm:flex sm:items-center mb-8">
        <div class="sm:flex-auto">
            <h1 class="text-3xl font-bold text-gray-900">Gestion des heures</h1>
            <p class="mt-2 text-gray-600">Suivi du temps de travail et heures supplémentaires</p>
        </div>
        <div class="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
            <button type="button" onclick="openModal('addHeuresModal')" class="inline-flex items-center justify-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 sm:w-auto">
                Saisir les heures
            </button>
        </div>
    </div>

    <!-- Filtres et sélection -->
    <div class="bg-white shadow rounded-lg mb-6">
        <div class="px-4 py-5 sm:p-6">
            <div class="grid grid-cols-1 gap-4 sm:grid-cols-3">
                <div>
                    <label for="collaborateurFilter" class="block text-sm font-medium text-gray-700">Collaborateur</label>
                    <select id="collaborateurFilter" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                        <option value="">Tous les collaborateurs</option>
                        {% for collaborateur in collaborateurs %}
                            <option value="{{ collaborateur.id }}">{{ collaborateur.nomComplet }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div>
                    <label for="semaineDebut" class="block text-sm font-medium text-gray-700">Semaine début</label>
                    <input type="week" id="semaineDebut" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                </div>
                <div>
                    <label for="semaineFin" class="block text-sm font-medium text-gray-700">Semaine fin</label>
                    <input type="week" id="semaineFin" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                </div>
            </div>
            <div class="mt-4">
                <button type="button" onclick="loadHeuresData()" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700">
                    Filtrer
                </button>
            </div>
        </div>
    </div>

    <!-- Résumé des heures supplémentaires -->
    <div class="grid grid-cols-1 gap-5 sm:grid-cols-3 mb-8">
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-orange-500 rounded-md flex items-center justify-center">
                            <span class="text-white text-sm font-medium">⏰</span>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Semaines avec H.S.</dt>
                            <dd class="text-lg font-medium text-gray-900" id="semainesHS">{{ semainesAvecHS|length }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-red-500 rounded-md flex items-center justify-center">
                            <span class="text-white text-sm font-medium">📈</span>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Total H.S.</dt>
                            <dd class="text-lg font-medium text-gray-900" id="totalHS">-</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                            <span class="text-white text-sm font-medium">📊</span>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Moyenne/semaine</dt>
                            <dd class="text-lg font-medium text-gray-900" id="moyenneHS">-</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Tableau des heures -->
    <div class="bg-white shadow overflow-hidden sm:rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Détail des heures</h3>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200" id="heuresTable">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Collaborateur</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Semaine</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Heures saisies</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Heures normales</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">H.S. x1.25</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">H.S. x1.50</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200" id="heuresTableBody">
                        <!-- Les données seront chargées dynamiquement -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Modal de saisie des heures -->
<div id="addHeuresModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Saisir les heures</h3>
            <form id="addHeuresForm">
                <div class="space-y-4">
                    <div>
                        <label for="collaborateurHeures" class="block text-sm font-medium text-gray-700">Collaborateur</label>
                        <select id="collaborateurHeures" name="collaborateurId" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                            <option value="">Sélectionner un collaborateur</option>
                            {% for collaborateur in collaborateurs %}
                                <option value="{{ collaborateur.id }}">{{ collaborateur.nomComplet }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div>
                        <label for="semaineAnnee" class="block text-sm font-medium text-gray-700">Semaine</label>
                        <input type="week" id="semaineAnnee" name="semaineAnnee" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                    </div>
                    <div>
                        <label for="heuresSaisies" class="block text-sm font-medium text-gray-700">Heures travaillées</label>
                        <input type="number" id="heuresSaisies" name="heuresSaisies" min="0" max="168" step="0.5" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                    </div>
                </div>
                <div class="flex justify-end space-x-3 mt-6">
                    <button type="button" onclick="closeModal('addHeuresModal')" class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200">
                        Annuler
                    </button>
                    <button type="submit" class="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700">
                        Enregistrer
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function openModal(modalId) {
    document.getElementById(modalId).classList.remove('hidden');
}

function closeModal(modalId) {
    document.getElementById(modalId).classList.add('hidden');
}

// Charger les données d'heures
async function loadHeuresData() {
    const collaborateurId = document.getElementById('collaborateurFilter').value;
    const semaineDebut = document.getElementById('semaineDebut').value;
    const semaineFin = document.getElementById('semaineFin').value;

    try {
        let url = "{{ path('api_semaine_travail_index') }}";
        const params = new URLSearchParams();

        if (collaborateurId) params.append('collaborateur', collaborateurId);
        if (semaineDebut) params.append('debut', semaineDebut.replace('-W', '-'));
        if (semaineFin) params.append('fin', semaineFin.replace('-W', '-'));

        if (params.toString()) {
            url += '?' + params.toString();
        }

        const response = await ajax.get(url);
        displayHeuresData(response.data);
    } catch (error) {
        console.error('Erreur lors du chargement des données:', error);
    }
}

function displayHeuresData(semaines) {
    const tbody = document.getElementById('heuresTableBody');
    tbody.innerHTML = '';

    let totalHS = 0;
    let nombreSemaines = 0;

    semaines.forEach(semaine => {
        // Calculer les heures supplémentaires (simulation côté client)
        const horaireHebdo = semaine.collaborateur ? semaine.collaborateur.horaireHebdo : 35;
        const heuresSupp = Math.max(0, semaine.heuresSaisies - horaireHebdo);
        const heuresSupp25 = Math.min(heuresSupp, 8); // Première tranche
        const heuresSupp50 = Math.max(0, heuresSupp - 8); // Deuxième tranche

        totalHS += heuresSupp;
        nombreSemaines++;

        const row = document.createElement('tr');
        row.innerHTML = `
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                ${semaine.collaborateur ? semaine.collaborateur.nom + ' ' + semaine.collaborateur.prenom : 'N/A'}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${semaine.semaineAnnee}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${semaine.heuresSaisies}h</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${Math.min(semaine.heuresSaisies, horaireHebdo)}h</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${heuresSupp25}h</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${heuresSupp50}h</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <button onclick="editSemaine(${semaine.id})" class="text-blue-600 hover:text-blue-900">Modifier</button>
            </td>
        `;
        tbody.appendChild(row);
    });

    // Mettre à jour les statistiques
    document.getElementById('totalHS').textContent = totalHS.toFixed(1) + 'h';
    document.getElementById('moyenneHS').textContent = nombreSemaines > 0 ? (totalHS / nombreSemaines).toFixed(1) + 'h' : '0h';
}

function editSemaine(id) {
    // TODO: Implémenter la modification
    alert('Modification de la semaine ' + id);
}

// Gestion du formulaire de saisie
document.getElementById('addHeuresForm').addEventListener('submit', async function(e) {
    e.preventDefault();

    const formData = new FormData(e.target);
    const semaineValue = formData.get('semaineAnnee');
    const semaineFormatted = semaineValue.replace('-W', '-');

    const data = {
        collaborateurId: parseInt(formData.get('collaborateurId')),
        semaineAnnee: semaineFormatted,
        heuresSaisies: parseFloat(formData.get('heuresSaisies'))
    };

    try {
        const response = await ajax.post("{{ path('api_semaine_travail_create') }}", data);
        if (response.status === 201) {
            closeModal('addHeuresModal');
            loadHeuresData();
        }
    } catch (error) {
        if (error.response && error.response.status === 409) {
            alert('Cette semaine existe déjà pour ce collaborateur');
        } else {
            alert('Erreur lors de l\'enregistrement des heures');
        }
        console.error(error);
    }
});

// Charger les données au démarrage
document.addEventListener('DOMContentLoaded', function() {
    loadHeuresData();
});
</script>
{% endblock %}

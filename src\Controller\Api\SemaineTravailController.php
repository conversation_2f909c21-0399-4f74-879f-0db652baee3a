<?php

namespace App\Controller\Api;

use App\Entity\SemaineTravail;
use App\Repository\SemaineTravailRepository;
use App\Repository\UserRepository;
use App\Service\HeureCalculatorService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Serializer\SerializerInterface;
use Symfony\Component\Validator\Validator\ValidatorInterface;

#[Route('/api/semaines-travail', name: 'api_semaine_travail_')]
class SemaineTravailController extends AbstractController
{
    public function __construct(
        private EntityManagerInterface $entityManager,
        private SerializerInterface $serializer,
        private ValidatorInterface $validator,
        private SemaineTravailRepository $semaineTravailRepository,
        private UserRepository $userRepository,
        private HeureCalculatorService $heureCalculator
    ) {}

    #[Route('', name: 'index', methods: ['GET'])]
    public function index(Request $request): JsonResponse
    {
        $userId = $request->query->get('user');
        $semaineDebut = $request->query->get('debut');
        $semaineFin = $request->query->get('fin');
        $typeContrat = $request->query->get('typeContrat');
        $seuil = $request->query->get('seuil');
        $page = max(1, (int) $request->query->get('page', 1));
        $limit = max(1, min(100, (int) $request->query->get('limit', 20)));

        // Construire les critères de filtrage
        $criteria = [];
        if ($userId) {
            $criteria['user'] = $userId;
        }
        if ($semaineDebut) {
            $criteria['semaineDebut'] = $semaineDebut;
        }
        if ($semaineFin) {
            $criteria['semaineFin'] = $semaineFin;
        }
        if ($typeContrat) {
            $criteria['typeContrat'] = $typeContrat;
        }

        // Récupérer les semaines avec filtres
        $semaines = $this->semaineTravailRepository->findWithFilters($criteria);

        // Pagination
        $total = count($semaines);
        $offset = ($page - 1) * $limit;
        $semaines = array_slice($semaines, $offset, $limit);

        // Calculs côté serveur
        $semainesAvecCalculs = [];
        $totalHS = 0;
        $nombreSemaines = 0;

        foreach ($semaines as $semaine) {
            $calcul = $this->heureCalculator->calculHeuresSemaine($semaine);
            $totalHeuresSupp = $calcul['heuresSupp25'] + $calcul['heuresSupp50'];

            // Appliquer le filtre de seuil d'heures supplémentaires
            if ($seuil === 'avec' && $totalHeuresSupp <= 0) {
                continue;
            }
            if ($seuil === 'sans' && $totalHeuresSupp > 0) {
                continue;
            }

            $semainesAvecCalculs[] = [
                'id' => $semaine->getId(),
                'user' => $semaine->getUser(),
                'collaborateur' => $semaine->getCollaborateur(), // Compatibilité
                'semaineAnnee' => $semaine->getSemaineAnnee(),
                'heuresSaisies' => $semaine->getHeuresSaisies(),
                'heuresNormales' => $calcul['heuresNormales'],
                'heuresSupp25' => $calcul['heuresSupp25'],
                'heuresSupp50' => $calcul['heuresSupp50'],
                'totalHeuresSupp' => $totalHeuresSupp,
                'source' => $semaine->getSource() ?? 'manuelle'
            ];

            $totalHS += $totalHeuresSupp;
            $nombreSemaines++;
        }

        // Export CSV si demandé
        if ($request->query->get('export') === 'csv') {
            return $this->exportToCsv($semainesAvecCalculs);
        }

        return $this->json([
            'data' => $semainesAvecCalculs,
            'pagination' => [
                'page' => $page,
                'limit' => $limit,
                'total' => $total,
                'pages' => ceil($total / $limit)
            ],
            'statistiques' => [
                'totalHS' => $totalHS,
                'moyenneHS' => $nombreSemaines > 0 ? $totalHS / $nombreSemaines : 0,
                'semainesAvecHS' => $nombreSemaines
            ]
        ], Response::HTTP_OK, [], [
            'groups' => ['semaine_travail:read', 'user:read', 'collaborateur:read']
        ]);
    }

    #[Route('/{id}', name: 'show', methods: ['GET'])]
    public function show(SemaineTravail $semaineTravail): JsonResponse
    {
        $calcul = $this->heureCalculator->calculHeuresSemaine($semaineTravail);

        return $this->json([
            'semaineTravail' => $semaineTravail,
            'calcul' => $calcul
        ], Response::HTTP_OK, [], [
            'groups' => ['semaine_travail:read', 'semaine_travail:detail']
        ]);
    }

    #[Route('', name: 'create', methods: ['POST'])]
    public function create(Request $request): JsonResponse
    {
        $data = json_decode($request->getContent(), true);

        $collaborateur = $this->collaborateurRepository->find($data['collaborateurId'] ?? null);
        if (!$collaborateur) {
            return $this->json(['error' => 'Collaborateur non trouvé'], Response::HTTP_BAD_REQUEST);
        }

        // Vérifier si la semaine existe déjà
        $semaineExistante = $this->semaineTravailRepository->findByCollaborateurAndSemaine(
            $collaborateur,
            $data['semaineAnnee'] ?? ''
        );

        if ($semaineExistante) {
            return $this->json(['error' => 'Cette semaine existe déjà pour ce collaborateur'], Response::HTTP_CONFLICT);
        }

        $semaineTravail = new SemaineTravail();
        $semaineTravail->setCollaborateur($collaborateur)
                      ->setSemaineAnnee($data['semaineAnnee'] ?? '')
                      ->setHeuresSaisies($data['heuresSaisies'] ?? 0);

        $errors = $this->validator->validate($semaineTravail);
        if (count($errors) > 0) {
            return $this->json(['errors' => (string) $errors], Response::HTTP_BAD_REQUEST);
        }

        $this->entityManager->persist($semaineTravail);
        $this->entityManager->flush();

        return $this->json($semaineTravail, Response::HTTP_CREATED, [], [
            'groups' => ['semaine_travail:read']
        ]);
    }

    #[Route('/{id}', name: 'update', methods: ['PUT'])]
    public function update(Request $request, SemaineTravail $semaineTravail): JsonResponse
    {
        $data = json_decode($request->getContent(), true);

        if (isset($data['heuresSaisies'])) {
            $semaineTravail->setHeuresSaisies($data['heuresSaisies']);
        }

        if (isset($data['semaineAnnee'])) {
            // Vérifier l'unicité si on change la semaine
            $semaineExistante = $this->semaineTravailRepository->findByCollaborateurAndSemaine(
                $semaineTravail->getCollaborateur(),
                $data['semaineAnnee']
            );

            if ($semaineExistante && $semaineExistante !== $semaineTravail) {
                return $this->json(['error' => 'Cette semaine existe déjà pour ce collaborateur'], Response::HTTP_CONFLICT);
            }

            $semaineTravail->setSemaineAnnee($data['semaineAnnee']);
        }

        $errors = $this->validator->validate($semaineTravail);
        if (count($errors) > 0) {
            return $this->json(['errors' => (string) $errors], Response::HTTP_BAD_REQUEST);
        }

        $this->entityManager->flush();

        return $this->json($semaineTravail, Response::HTTP_OK, [], [
            'groups' => ['semaine_travail:read']
        ]);
    }

    #[Route('/{id}', name: 'delete', methods: ['DELETE'])]
    public function delete(SemaineTravail $semaineTravail): JsonResponse
    {
        $this->entityManager->remove($semaineTravail);
        $this->entityManager->flush();

        return $this->json(null, Response::HTTP_NO_CONTENT);
    }

    #[Route('/heures-supplementaires', name: 'heures_supplementaires', methods: ['GET'])]
    public function heuresSupplementaires(): JsonResponse
    {
        $semaines = $this->semaineTravailRepository->findWithHeuresSupplementaires();

        $result = [];
        foreach ($semaines as $semaine) {
            $calcul = $this->heureCalculator->calculHeuresSemaine($semaine);
            $result[] = [
                'semaine' => $semaine,
                'calcul' => $calcul
            ];
        }

        return $this->json($result, Response::HTTP_OK, [], [
            'groups' => ['semaine_travail:read']
        ]);
    }

    #[Route('/statistiques', name: 'statistiques', methods: ['GET'])]
    public function statistiques(): JsonResponse
    {
        $stats = $this->semaineTravailRepository->getStatistiquesHebdomadaires();
        return $this->json($stats);
    }

    #[Route('/collaborateur/{collaborateurId}/periode', name: 'periode_collaborateur', methods: ['GET'])]
    public function periodeCollaborateur(Request $request, int $collaborateurId): JsonResponse
    {
        $collaborateur = $this->collaborateurRepository->find($collaborateurId);
        if (!$collaborateur) {
            return $this->json(['error' => 'Collaborateur non trouvé'], Response::HTTP_NOT_FOUND);
        }

        $semaineDebut = $request->query->get('debut');
        $semaineFin = $request->query->get('fin');

        $semaines = $this->semaineTravailRepository->findByCollaborateur($collaborateur);

        if ($semaineDebut && $semaineFin) {
            $semaines = array_filter($semaines, function($semaine) use ($semaineDebut, $semaineFin) {
                return $semaine->getSemaineAnnee() >= $semaineDebut && $semaine->getSemaineAnnee() <= $semaineFin;
            });
        }

        $calculPeriode = $this->heureCalculator->calculHeuresPeriode($collaborateur, $semaines);

        return $this->json([
            'collaborateur' => $collaborateur,
            'semaines' => $semaines,
            'calculPeriode' => $calculPeriode
        ], Response::HTTP_OK, [], [
            'groups' => ['semaine_travail:read', 'collaborateur:read']
        ]);
    }

    #[Route('/semaine-actuelle', name: 'semaine_actuelle', methods: ['GET'])]
    public function semaineActuelle(): JsonResponse
    {
        $semaineActuelle = SemaineTravail::getSemaineActuelle();

        return $this->json([
            'semaineActuelle' => $semaineActuelle,
            'dateDebut' => (new \DateTime())->setISODate(
                (int) substr($semaineActuelle, 0, 4),
                (int) substr($semaineActuelle, 5, 2)
            )->format('Y-m-d'),
            'dateFin' => (new \DateTime())->setISODate(
                (int) substr($semaineActuelle, 0, 4),
                (int) substr($semaineActuelle, 5, 2)
            )->modify('+6 days')->format('Y-m-d')
        ]);
    }

    private function exportToCsv(array $semaines): Response
    {
        $csv = "Collaborateur,Semaine,Heures saisies,Heures normales,H.S. x1.25,H.S. x1.50,Total H.S.\n";

        foreach ($semaines as $semaine) {
            $collaborateur = $semaine['collaborateur'];
            $nomComplet = $collaborateur ? $collaborateur->getNomComplet() : 'N/A';

            $csv .= sprintf(
                "%s,%s,%.1f,%.1f,%.1f,%.1f,%.1f\n",
                $nomComplet,
                $semaine['semaineAnnee'],
                $semaine['heuresSaisies'],
                $semaine['heuresNormales'],
                $semaine['heuresSupp25'],
                $semaine['heuresSupp50'],
                $semaine['totalHeuresSupp']
            );
        }

        $response = new Response($csv);
        $response->headers->set('Content-Type', 'text/csv');
        $response->headers->set('Content-Disposition', 'attachment; filename="heures_' . date('Y-m-d') . '.csv"');

        return $response;
    }
}

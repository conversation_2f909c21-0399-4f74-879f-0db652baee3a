<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Suppression de la colonne collaborateur_id de la table semaine_travail
 */
final class Version20241218000002 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Supprime la colonne collaborateur_id de la table semaine_travail et met à jour les contraintes';
    }

    public function up(Schema $schema): void
    {
        // Supprimer l'ancienne contrainte unique
        $this->addSql('DROP INDEX collaborateur_semaine_unique ON semaine_travail');
        
        // Supprimer la contrainte de clé étrangère
        $this->addSql('ALTER TABLE semaine_travail DROP FOREIGN KEY FK_semaine_travail_collaborateur');
        
        // Supprimer la colonne
        $this->addSql('ALTER TABLE semaine_travail DROP COLUMN collaborateur_id');
        
        // Créer la nouvelle contrainte unique
        $this->addSql('CREATE UNIQUE INDEX user_semaine_unique ON semaine_travail (user_id, semaine_annee)');
    }

    public function down(Schema $schema): void
    {
        // Supprimer la nouvelle contrainte
        $this->addSql('DROP INDEX user_semaine_unique ON semaine_travail');
        
        // Recréer la colonne
        $this->addSql('ALTER TABLE semaine_travail ADD collaborateur_id INT DEFAULT NULL');
        
        // Recréer la contrainte de clé étrangère
        $this->addSql('ALTER TABLE semaine_travail ADD CONSTRAINT FK_semaine_travail_collaborateur FOREIGN KEY (collaborateur_id) REFERENCES collaborateur (id)');
        
        // Recréer l'ancienne contrainte unique
        $this->addSql('CREATE UNIQUE INDEX collaborateur_semaine_unique ON semaine_travail (collaborateur_id, semaine_annee)');
    }
}

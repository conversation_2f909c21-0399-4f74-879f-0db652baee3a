<?php

namespace App\Controller\Api;

use App\Entity\Collaborateur;
use App\Service\HeuresSynchronisationService;
use App\Repository\CollaborateurRepository;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

#[Route('/api/heures-synchronisation', name: 'api_heures_sync_')]
class HeuresSynchronisationController extends AbstractController
{
    public function __construct(
        private HeuresSynchronisationService $synchronisationService,
        private CollaborateurRepository $collaborateurRepository
    ) {}

    #[Route('/preview/{id}', name: 'preview', methods: ['GET'])]
    public function preview(Request $request, Collaborateur $collaborateur): JsonResponse
    {
        $dateDebut = $request->query->get('debut') ? new \DateTime($request->query->get('debut')) : null;
        $dateFin = $request->query->get('fin') ? new \DateTime($request->query->get('fin')) : null;

        $preview = $this->synchronisationService->previewSynchronisation($collaborateur, $dateDebut, $dateFin);

        return $this->json([
            'collaborateur' => $collaborateur->getNomComplet(),
            'preview' => $preview,
            'statistiques' => [
                'totalSemaines' => count($preview),
                'semainesAvecConflits' => count(array_filter($preview, fn($p) => $p['conflit'])),
                'totalHeuresCalculees' => array_sum(array_column($preview, 'heuresCalculees')),
                'totalHeuresActuelles' => array_sum(array_column($preview, 'heuresActuelles'))
            ]
        ]);
    }

    #[Route('/synchroniser/{id}', name: 'sync_collaborateur', methods: ['POST'])]
    public function synchroniserCollaborateur(Request $request, Collaborateur $collaborateur): JsonResponse
    {
        $data = json_decode($request->getContent(), true);

        $dateDebut = isset($data['debut']) ? new \DateTime($data['debut']) : null;
        $dateFin = isset($data['fin']) ? new \DateTime($data['fin']) : null;
        $forceOverwrite = $data['forceOverwrite'] ?? false;

        try {
            $resultats = $this->synchronisationService->synchroniserCollaborateur(
                $collaborateur,
                $dateDebut,
                $dateFin,
                $forceOverwrite
            );

            $statistiques = [
                'totalSemaines' => count($resultats),
                'creations' => count(array_filter($resultats, fn($r) => $r['action'] === 'creation')),
                'miseAJour' => count(array_filter($resultats, fn($r) => $r['action'] === 'mise_a_jour')),
                'conflits' => count(array_filter($resultats, fn($r) => $r['action'] === 'conflit')),
                'aucuneAction' => count(array_filter($resultats, fn($r) => $r['action'] === 'aucune'))
            ];

            return $this->json([
                'success' => true,
                'collaborateurId' => $collaborateur->getId(),
                'collaborateurNom' => $collaborateur->getNomComplet(),
                'resultats' => $resultats,
                'statistiques' => $statistiques
            ]);

        } catch (\Exception $e) {
            return $this->json([
                'success' => false,
                'error' => $e->getMessage()
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    #[Route('/synchroniser-tous', name: 'sync_tous', methods: ['POST'])]
    public function synchroniserTous(Request $request): JsonResponse
    {
        $data = json_decode($request->getContent(), true);

        $dateDebut = isset($data['debut']) ? new \DateTime($data['debut']) : null;
        $dateFin = isset($data['fin']) ? new \DateTime($data['fin']) : null;
        $forceOverwrite = $data['forceOverwrite'] ?? false;

        try {
            $resultats = $this->synchronisationService->synchroniserTous($dateDebut, $dateFin, $forceOverwrite);

            $statistiquesGlobales = [
                'collaborateursTraites' => count($resultats),
                'totalSemaines' => 0,
                'totalCreations' => 0,
                'totalMiseAJour' => 0,
                'totalConflits' => 0
            ];

            foreach ($resultats as $resultatCollaborateur) {
                $resultatsCollaborateur = $resultatCollaborateur['resultats'];
                $statistiquesGlobales['totalSemaines'] += count($resultatsCollaborateur);
                $statistiquesGlobales['totalCreations'] += count(array_filter($resultatsCollaborateur, fn($r) => $r['action'] === 'creation'));
                $statistiquesGlobales['totalMiseAJour'] += count(array_filter($resultatsCollaborateur, fn($r) => $r['action'] === 'mise_a_jour'));
                $statistiquesGlobales['totalConflits'] += count(array_filter($resultatsCollaborateur, fn($r) => $r['action'] === 'conflit'));
            }

            return $this->json([
                'success' => true,
                'resultats' => $resultats,
                'statistiques' => $statistiquesGlobales
            ]);

        } catch (\Exception $e) {
            return $this->json([
                'success' => false,
                'error' => $e->getMessage()
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    #[Route('/preview-tous', name: 'preview_tous', methods: ['GET'])]
    public function previewTous(Request $request): JsonResponse
    {
        $dateDebut = $request->query->get('debut') ? new \DateTime($request->query->get('debut')) : null;
        $dateFin = $request->query->get('fin') ? new \DateTime($request->query->get('fin')) : null;

        $collaborateurs = $this->collaborateurRepository->findAll();
        $previews = [];
        $statistiquesGlobales = [
            'totalCollaborateurs' => count($collaborateurs),
            'totalSemaines' => 0,
            'totalConflits' => 0,
            'totalHeuresCalculees' => 0
        ];

        foreach ($collaborateurs as $collaborateur) {
            $preview = $this->synchronisationService->previewSynchronisation($collaborateur, $dateDebut, $dateFin);

            if (!empty($preview)) {
                $previews[$collaborateur->getId()] = [
                    'collaborateurId' => $collaborateur->getId(),
                    'collaborateurNom' => $collaborateur->getNomComplet(),
                    'preview' => $preview,
                    'statistiques' => [
                        'totalSemaines' => count($preview),
                        'semainesAvecConflits' => count(array_filter($preview, fn($p) => $p['conflit'])),
                        'totalHeuresCalculees' => array_sum(array_column($preview, 'heuresCalculees'))
                    ]
                ];

                $statistiquesGlobales['totalSemaines'] += count($preview);
                $statistiquesGlobales['totalConflits'] += count(array_filter($preview, fn($p) => $p['conflit']));
                $statistiquesGlobales['totalHeuresCalculees'] += array_sum(array_column($preview, 'heuresCalculees'));
            }
        }

        return $this->json([
            'previews' => $previews,
            'statistiques' => $statistiquesGlobales
        ]);
    }
}

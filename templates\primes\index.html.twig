{% extends 'base.html.twig' %}

{% block title %}Gestion des primes - OSI Manager{% endblock %}

{% block body %}
<div class="px-4 py-6 sm:px-0">
    <!-- En-tête -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900">Gestion des primes</h1>
        <p class="mt-2 text-gray-600">Calcul et suivi des primes journalières</p>
    </div>

    <!-- Barème des primes -->
    <div class="bg-white shadow rounded-lg mb-8">
        <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Barème des primes journalières</h3>
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Zone Euro -->
                <div>
                    <h4 class="text-md font-medium text-blue-900 mb-3">Zone Euro</h4>
                    <div class="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
                        <table class="min-w-full divide-y divide-gray-300">
                            <thead class="bg-blue-50">
                                <tr>
                                    <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">Type</th>
                                    <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">Niveau 1</th>
                                    <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">Niveau 2</th>
                                </tr>
                            </thead>
                            <tbody class="divide-y divide-gray-200 bg-white">
                                <tr>
                                    <td class="px-3 py-2 text-sm text-gray-900">Semaine</td>
                                    <td class="px-3 py-2 text-sm text-gray-900">{{ bareme.EURO.semaine[1] }}€</td>
                                    <td class="px-3 py-2 text-sm text-gray-900">{{ bareme.EURO.semaine[2] }}€</td>
                                </tr>
                                <tr>
                                    <td class="px-3 py-2 text-sm text-gray-900">WE travaillé</td>
                                    <td class="px-3 py-2 text-sm text-gray-900">{{ bareme.EURO.weekend_travaille[1] }}€</td>
                                    <td class="px-3 py-2 text-sm text-gray-900">{{ bareme.EURO.weekend_travaille[2] }}€</td>
                                </tr>
                                <tr>
                                    <td class="px-3 py-2 text-sm text-gray-900">WE non travaillé</td>
                                    <td class="px-3 py-2 text-sm text-gray-900">{{ bareme.EURO.weekend_non_travaille[1] }}€</td>
                                    <td class="px-3 py-2 text-sm text-gray-900">{{ bareme.EURO.weekend_non_travaille[2] }}€</td>
                                </tr>
                                <tr>
                                    <td class="px-3 py-2 text-sm text-gray-900">WE voyage</td>
                                    <td class="px-3 py-2 text-sm text-gray-900">{{ bareme.EURO.weekend_voyage[1] }}€</td>
                                    <td class="px-3 py-2 text-sm text-gray-900">{{ bareme.EURO.weekend_voyage[2] }}€</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Hors Zone Euro -->
                <div>
                    <h4 class="text-md font-medium text-green-900 mb-3">Hors Zone Euro</h4>
                    <div class="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
                        <table class="min-w-full divide-y divide-gray-300">
                            <thead class="bg-green-50">
                                <tr>
                                    <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">Type</th>
                                    <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">Niveau 1</th>
                                    <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">Niveau 2</th>
                                </tr>
                            </thead>
                            <tbody class="divide-y divide-gray-200 bg-white">
                                <tr>
                                    <td class="px-3 py-2 text-sm text-gray-900">Semaine</td>
                                    <td class="px-3 py-2 text-sm text-gray-900">{{ bareme.HORS_EURO.semaine[1] }}€</td>
                                    <td class="px-3 py-2 text-sm text-gray-900">{{ bareme.HORS_EURO.semaine[2] }}€</td>
                                </tr>
                                <tr>
                                    <td class="px-3 py-2 text-sm text-gray-900">WE travaillé</td>
                                    <td class="px-3 py-2 text-sm text-gray-900">{{ bareme.HORS_EURO.weekend_travaille[1] }}€</td>
                                    <td class="px-3 py-2 text-sm text-gray-900">{{ bareme.HORS_EURO.weekend_travaille[2] }}€</td>
                                </tr>
                                <tr>
                                    <td class="px-3 py-2 text-sm text-gray-900">WE non travaillé</td>
                                    <td class="px-3 py-2 text-sm text-gray-900">{{ bareme.HORS_EURO.weekend_non_travaille[1] }}€</td>
                                    <td class="px-3 py-2 text-sm text-gray-900">{{ bareme.HORS_EURO.weekend_non_travaille[2] }}€</td>
                                </tr>
                                <tr>
                                    <td class="px-3 py-2 text-sm text-gray-900">WE voyage</td>
                                    <td class="px-3 py-2 text-sm text-gray-900">{{ bareme.HORS_EURO.weekend_voyage[1] }}€</td>
                                    <td class="px-3 py-2 text-sm text-gray-900">{{ bareme.HORS_EURO.weekend_voyage[2] }}€</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filtres -->
    <div class="bg-white shadow rounded-lg mb-6">
        <div class="px-4 py-5 sm:p-6">
            <div class="grid grid-cols-1 gap-4 sm:grid-cols-4">
                <div>
                    <label for="userPrimes" class="block text-sm font-medium text-gray-700">Utilisateur</label>
                    <select id="userPrimes" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                        <option value="">Tous les utilisateurs</option>
                    </select>
                </div>
                <div>
                    <label for="dateDebutPrimes" class="block text-sm font-medium text-gray-700">Date début</label>
                    <input type="date" id="dateDebutPrimes" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                </div>
                <div>
                    <label for="dateFinPrimes" class="block text-sm font-medium text-gray-700">Date fin</label>
                    <input type="date" id="dateFinPrimes" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                </div>
                <div class="flex items-end">
                    <button type="button" onclick="calculerPrimes()" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700">
                        Calculer
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Résultats des primes -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Calcul des primes</h3>

            <!-- Résumé -->
            <div class="grid grid-cols-1 gap-5 sm:grid-cols-3 mb-6" id="resumePrimes" style="display: none;">
                <div class="bg-blue-50 overflow-hidden shadow rounded-lg">
                    <div class="p-5">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <span class="text-2xl">💰</span>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 truncate">Total primes</dt>
                                    <dd class="text-lg font-medium text-gray-900" id="totalPrimes">0€</dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-green-50 overflow-hidden shadow rounded-lg">
                    <div class="p-5">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <span class="text-2xl">📊</span>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 truncate">Nombre missions</dt>
                                    <dd class="text-lg font-medium text-gray-900" id="nombreMissions">0</dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-purple-50 overflow-hidden shadow rounded-lg">
                    <div class="p-5">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <span class="text-2xl">📈</span>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 truncate">Prime moyenne</dt>
                                    <dd class="text-lg font-medium text-gray-900" id="primeMoyenne">0€</dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tableau détaillé -->
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200" id="primesTable" style="display: none;">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Mission</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Utilisateur</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Pays</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Zone</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Niveau</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Durée</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Prime totale</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200" id="primesTableBody">
                        <!-- Les données seront chargées dynamiquement -->
                    </tbody>
                </table>
            </div>

            <div id="noPrimesMessage" class="text-center py-8 text-gray-500">
                Sélectionnez des critères et cliquez sur "Calculer" pour voir les primes
            </div>
        </div>
    </div>
</div>

<script>
// URLs pour les appels API
const apiUrls = {
    missionPrimes: "{{ path('api_mission_primes', {id: 'MISSION_ID'}) }}",
    missionDetail: "{{ path('app_mission_detail', {id: 'MISSION_ID'}) }}"
};

// Charger les utilisateurs au démarrage
document.addEventListener('DOMContentLoaded', async function() {
    try {
        const response = await ajax.get("{{ path('api_user_index') }}");
        const select = document.getElementById('userPrimes');

        response.forEach(user => {
            const option = document.createElement('option');
            option.value = user.id;
            option.textContent = `${user.prenom} ${user.nom}`;
            select.appendChild(option);
        });
    } catch (error) {
        console.error('Erreur lors du chargement des utilisateurs:', error);
    }
});

async function calculerPrimes() {
    const userId = document.getElementById('userPrimes').value;
    const dateDebut = document.getElementById('dateDebutPrimes').value;
    const dateFin = document.getElementById('dateFinPrimes').value;

    try {
        // Construire l'URL avec les paramètres
        let url = "{{ path('api_mission_index') }}";
        const params = new URLSearchParams();

        if (userId) params.append('user', userId);
        if (dateDebut) params.append('debut', dateDebut);
        if (dateFin) params.append('fin', dateFin);

        if (params.toString()) {
            url += '?' + params.toString();
        }

        const response = await ajax.get(url);
        const missions = Array.isArray(response) ? response : response.data || [];

        // Calculer les primes pour chaque mission
        const primesData = [];
        let totalPrimes = 0;

        for (const mission of missions) {
            try {
                const primeUrl = apiUrls.missionPrimes.replace('MISSION_ID', mission.id);
                const primeResponse = await ajax.get(primeUrl);
                const primeData = {
                    mission: mission,
                    prime: primeResponse.primesTotales || 0
                };
                primesData.push(primeData);
                totalPrimes += primeData.prime;
            } catch (error) {
                console.error(`Erreur lors du calcul de la prime pour la mission ${mission.id}:`, error);
            }
        }

        displayPrimesResults(primesData, totalPrimes);

    } catch (error) {
        console.error('Erreur lors du calcul des primes:', error);
        alert('Erreur lors du calcul des primes');
    }
}

function displayPrimesResults(primesData, totalPrimes) {
    // Afficher le résumé
    document.getElementById('resumePrimes').style.display = 'grid';
    document.getElementById('totalPrimes').textContent = totalPrimes.toFixed(2) + '€';
    document.getElementById('nombreMissions').textContent = primesData.length;
    document.getElementById('primeMoyenne').textContent = primesData.length > 0 ? (totalPrimes / primesData.length).toFixed(2) + '€' : '0€';

    // Afficher le tableau
    const tbody = document.getElementById('primesTableBody');
    tbody.innerHTML = '';

    if (primesData.length > 0) {
        document.getElementById('primesTable').style.display = 'table';
        document.getElementById('noPrimesMessage').style.display = 'none';

        primesData.forEach(data => {
            const mission = data.mission;

            // Afficher les utilisateurs assignés à la mission
            let utilisateurs = 'Non assigné';
            if (mission.users && mission.users.length > 0) {
                utilisateurs = mission.users.map(user => `${user.prenom} ${user.nom}`).join(', ');
            }

            const row = document.createElement('tr');
            row.innerHTML = `
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${mission.titre}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${utilisateurs}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${mission.pays}</td>
                <td class="px-6 py-4 whitespace-nowrap">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${mission.zone === 'EURO' ? 'bg-blue-100 text-blue-800' : 'bg-green-100 text-green-800'}">
                        ${mission.zone === 'EURO' ? 'Zone Euro' : 'Hors Zone Euro'}
                    </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">Niveau ${mission.niveau}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${mission.dureeJours || 'N/A'} jour(s)</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">${data.prime.toFixed(2)}€</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <a href="${apiUrls.missionDetail.replace('MISSION_ID', mission.id)}" class="text-blue-600 hover:text-blue-900">Détail</a>
                </td>
            `;
            tbody.appendChild(row);
        });
    } else {
        document.getElementById('primesTable').style.display = 'none';
        document.getElementById('noPrimesMessage').style.display = 'block';
        document.getElementById('noPrimesMessage').textContent = 'Aucune mission trouvée pour les critères sélectionnés';
    }
}
</script>
{% endblock %}

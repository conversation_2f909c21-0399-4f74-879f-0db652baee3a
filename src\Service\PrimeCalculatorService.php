<?php

namespace App\Service;

use App\Entity\Mission;
use App\Entity\Segment;

class PrimeCalculatorService
{
    // Barème des primes journalières
    private const PRIMES = [
        Mission::ZONE_EURO => [
            'semaine' => [
                Mission::NIVEAU_1 => 130,
                Mission::NIVEAU_2 => 230,
            ],
            'weekend_travaille' => [
                Mission::NIVEAU_1 => 160,
                Mission::NIVEAU_2 => 335,
            ],
            'weekend_non_travaille' => [
                Mission::NIVEAU_1 => 90,
                Mission::NIVEAU_2 => 180,
            ],
            'weekend_voyage' => [
                Mission::NIVEAU_1 => 160,
                Mission::NIVEAU_2 => 160,
            ],
        ],
        Mission::ZONE_HORS_EURO => [
            'semaine' => [
                Mission::NIVEAU_1 => 150,
                Mission::NIVEAU_2 => 250,
            ],
            'weekend_travaille' => [
                Mission::NIVEAU_1 => 180,
                Mission::NIVEAU_2 => 355,
            ],
            'weekend_non_travaille' => [
                Mission::NIVEAU_1 => 110,
                Mission::NIVEAU_2 => 200,
            ],
            'weekend_voyage' => [
                Mission::NIVEAU_1 => 180,
                Mission::NIVEAU_2 => 180,
            ],
        ],
    ];

    /**
     * Calcule la prime journalière selon le barème
     */
    public function calculPrimeJournalier(\DateTimeInterface $jour, string $zone, int $niveau, string $typeJour): float
    {
        if (!isset(self::PRIMES[$zone])) {
            throw new \InvalidArgumentException("Zone inconnue: $zone");
        }

        if (!in_array($niveau, [Mission::NIVEAU_1, Mission::NIVEAU_2])) {
            throw new \InvalidArgumentException("Niveau invalide: $niveau");
        }

        $bareme = self::PRIMES[$zone];

        return match($typeJour) {
            'semaine' => $bareme['semaine'][$niveau],
            'weekend_travaille' => $bareme['weekend_travaille'][$niveau],
            'weekend_non_travaille' => $bareme['weekend_non_travaille'][$niveau],
            'weekend_voyage' => $bareme['weekend_voyage'][$niveau],
            default => throw new \InvalidArgumentException("Type de jour invalide: $typeJour")
        };
    }

    /**
     * Détermine le type de jour selon les segments
     */
    public function determinerTypeJour(\DateTimeInterface $jour, array $segments): string
    {
        $isWeekend = $this->isWeekend($jour);
        $hasVoyage = false;
        $hasTravail = false;

        foreach ($segments as $segment) {
            if (!$this->segmentConcerneJour($segment, $jour)) {
                continue;
            }

            if ($segment->isVoyage()) {
                $hasVoyage = true;
            } elseif ($segment->isIntervention() || $segment->isStandBy()) {
                $hasTravail = true;
            }
        }

        if (!$isWeekend) {
            return 'semaine';
        }

        if ($hasVoyage) {
            return 'weekend_voyage';
        }

        if ($hasTravail) {
            return 'weekend_travaille';
        }

        return 'weekend_non_travaille';
    }

    /**
     * Calcule les primes totales d'une mission
     */
    public function calculPrimesTotalesMission(Mission $mission): float
    {
        $total = 0;
        $segments = $mission->getSegments()->toArray();
        
        // Grouper les segments par jour
        $segmentsParJour = $this->grouperSegmentsParJour($segments);

        foreach ($segmentsParJour as $jour => $segmentsJour) {
            $dateJour = new \DateTime($jour);
            $typeJour = $this->determinerTypeJour($dateJour, $segmentsJour);
            $prime = $this->calculPrimeJournalier($dateJour, $mission->getZone(), $mission->getNiveau(), $typeJour);
            $total += $prime;
        }

        return $total;
    }

    /**
     * Calcule les primes d'un collaborateur sur une période
     */
    public function calculPrimesCollaborateur(array $missions, \DateTimeInterface $debut, \DateTimeInterface $fin): array
    {
        $primes = [];
        $totalGeneral = 0;

        foreach ($missions as $mission) {
            if ($this->missionDansPeriode($mission, $debut, $fin)) {
                $primeMission = $this->calculPrimesTotalesMission($mission);
                $primes[] = [
                    'mission' => $mission,
                    'prime' => $primeMission
                ];
                $totalGeneral += $primeMission;
            }
        }

        return [
            'primes' => $primes,
            'total' => $totalGeneral
        ];
    }

    /**
     * Vérifie si un segment concerne un jour donné
     */
    private function segmentConcerneJour(Segment $segment, \DateTimeInterface $jour): bool
    {
        $jourDebut = $jour->format('Y-m-d');
        $segmentDebut = $segment->getDateHeureDebut()->format('Y-m-d');
        $segmentFin = $segment->getDateHeureFin()->format('Y-m-d');

        return $jourDebut >= $segmentDebut && $jourDebut <= $segmentFin;
    }

    /**
     * Groupe les segments par jour
     */
    private function grouperSegmentsParJour(array $segments): array
    {
        $groupes = [];

        foreach ($segments as $segment) {
            $debut = $segment->getDateHeureDebut();
            $fin = $segment->getDateHeureFin();
            
            $current = clone $debut;
            $current->setTime(0, 0, 0);
            
            while ($current <= $fin) {
                $jour = $current->format('Y-m-d');
                if (!isset($groupes[$jour])) {
                    $groupes[$jour] = [];
                }
                $groupes[$jour][] = $segment;
                $current->modify('+1 day');
            }
        }

        return $groupes;
    }

    /**
     * Vérifie si c'est un week-end
     */
    private function isWeekend(\DateTimeInterface $date): bool
    {
        $dayOfWeek = (int) $date->format('N');
        return $dayOfWeek >= 6; // 6 = samedi, 7 = dimanche
    }

    /**
     * Vérifie si une mission est dans la période
     */
    private function missionDansPeriode(Mission $mission, \DateTimeInterface $debut, \DateTimeInterface $fin): bool
    {
        return $mission->getDateDebut() <= $fin && $mission->getDateFin() >= $debut;
    }

    /**
     * Obtient le barème complet des primes
     */
    public function getBareme(): array
    {
        return self::PRIMES;
    }
}

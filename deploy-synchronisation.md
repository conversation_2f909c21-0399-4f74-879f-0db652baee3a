# Déploiement de la fonctionnalité de synchronisation des heures

## Fichiers à transférer sur le serveur de production

### Nouveaux fichiers créés :
- `src/Controller/Api/HeuresSynchronisationController.php`
- `src/Service/HeuresSynchronisationService.php`

### Fichiers modifiés :
- `src/Repository/SegmentRepository.php` (ajout de la méthode `findByCollaborateurAndPeriod`)
- `src/Entity/SemaineTravail.php` (ajout du champ `source` et méthodes associées)
- `src/Controller/Api/SemaineTravailController.php` (inclusion de la source dans l'API)
- `templates/heures/index.html.twig` (interface de synchronisation et colonne source)

## Commandes à exécuter sur le serveur

1. **Migrer la base de données :**
   ```bash
   php bin/console doctrine:migrations:migrate --no-interaction
   ```

2. **Vider le cache :**
   ```bash
   php bin/console cache:clear --env=prod
   ```

3. **Vérifier les routes :**
   ```bash
   php bin/console debug:router | grep synchronisation
   ```

## Routes ajoutées

- `GET /api/heures-synchronisation/preview/{id}` - Aperçu pour un collaborateur
- `POST /api/heures-synchronisation/synchroniser/{id}` - Synchroniser un collaborateur  
- `GET /api/heures-synchronisation/preview-tous` - Aperçu global
- `POST /api/heures-synchronisation/synchroniser-tous` - Synchroniser tous

## Fonctionnalités ajoutées

1. **Synchronisation automatique** des heures à partir des segments de mission
2. **Interface utilisateur** avec modal de synchronisation
3. **Traçabilité** des sources (manuel vs automatique)
4. **Gestion des conflits** entre heures saisies et calculées
5. **Aperçu avant synchronisation** pour éviter les erreurs
6. **Indicateurs visuels** dans le tableau des heures

## Test après déploiement

1. Aller sur `/heures`
2. Cliquer sur "🔄 Synchroniser depuis les missions"
3. Tester l'aperçu pour un collaborateur
4. Vérifier que les nouvelles colonnes s'affichent correctement

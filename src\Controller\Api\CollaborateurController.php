<?php

namespace App\Controller\Api;

use App\Entity\Collaborateur;
use App\Repository\CollaborateurRepository;
use App\Service\HeureCalculatorService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Serializer\SerializerInterface;
use Symfony\Component\Validator\Validator\ValidatorInterface;

#[Route('/api/collaborateurs', name: 'api_collaborateur_')]
class CollaborateurController extends AbstractController
{
    public function __construct(
        private EntityManagerInterface $entityManager,
        private SerializerInterface $serializer,
        private ValidatorInterface $validator,
        private CollaborateurRepository $collaborateurRepository,
        private HeureCalculatorService $heureCalculator
    ) {}

    #[Route('', name: 'index', methods: ['GET'])]
    public function index(Request $request): JsonResponse
    {
        $role = $request->query->get('role');
        $search = $request->query->get('search');

        if ($role) {
            $collaborateurs = $this->collaborateurRepository->findByRole($role);
        } elseif ($search) {
            $collaborateurs = $this->collaborateurRepository->findByNomOrPrenom($search);
        } else {
            $collaborateurs = $this->collaborateurRepository->findAll();
        }

        return $this->json($collaborateurs, Response::HTTP_OK, [], [
            'groups' => ['collaborateur:read']
        ]);
    }

    #[Route('/{id}', name: 'show', methods: ['GET'])]
    public function show(Collaborateur $collaborateur): JsonResponse
    {
        return $this->json($collaborateur, Response::HTTP_OK, [], [
            'groups' => ['collaborateur:read', 'collaborateur:detail']
        ]);
    }

    #[Route('', name: 'create', methods: ['POST'])]
    public function create(Request $request): JsonResponse
    {
        $data = json_decode($request->getContent(), true);
        
        $collaborateur = new Collaborateur();
        $collaborateur->setNom($data['nom'] ?? '')
                     ->setPrenom($data['prenom'] ?? '')
                     ->setEmail($data['email'] ?? '')
                     ->setRole($data['role'] ?? '')
                     ->setHoraireHebdo($data['horaireHebdo'] ?? 35)
                     ->setForfaitJour($data['forfaitJour'] ?? false);

        $errors = $this->validator->validate($collaborateur);
        if (count($errors) > 0) {
            return $this->json(['errors' => (string) $errors], Response::HTTP_BAD_REQUEST);
        }

        $this->entityManager->persist($collaborateur);
        $this->entityManager->flush();

        return $this->json($collaborateur, Response::HTTP_CREATED, [], [
            'groups' => ['collaborateur:read']
        ]);
    }

    #[Route('/{id}', name: 'update', methods: ['PUT'])]
    public function update(Request $request, Collaborateur $collaborateur): JsonResponse
    {
        $data = json_decode($request->getContent(), true);

        if (isset($data['nom'])) $collaborateur->setNom($data['nom']);
        if (isset($data['prenom'])) $collaborateur->setPrenom($data['prenom']);
        if (isset($data['email'])) $collaborateur->setEmail($data['email']);
        if (isset($data['role'])) $collaborateur->setRole($data['role']);
        if (isset($data['horaireHebdo'])) $collaborateur->setHoraireHebdo($data['horaireHebdo']);
        if (isset($data['forfaitJour'])) $collaborateur->setForfaitJour($data['forfaitJour']);

        $errors = $this->validator->validate($collaborateur);
        if (count($errors) > 0) {
            return $this->json(['errors' => (string) $errors], Response::HTTP_BAD_REQUEST);
        }

        $this->entityManager->flush();

        return $this->json($collaborateur, Response::HTTP_OK, [], [
            'groups' => ['collaborateur:read']
        ]);
    }

    #[Route('/{id}', name: 'delete', methods: ['DELETE'])]
    public function delete(Collaborateur $collaborateur): JsonResponse
    {
        $this->entityManager->remove($collaborateur);
        $this->entityManager->flush();

        return $this->json(null, Response::HTTP_NO_CONTENT);
    }

    #[Route('/statistiques', name: 'statistiques', methods: ['GET'])]
    public function statistiques(): JsonResponse
    {
        $stats = $this->collaborateurRepository->getStatistiques();
        return $this->json($stats);
    }

    #[Route('/{id}/missions', name: 'missions', methods: ['GET'])]
    public function missions(Collaborateur $collaborateur): JsonResponse
    {
        $missions = $collaborateur->getMissions();
        
        return $this->json($missions, Response::HTTP_OK, [], [
            'groups' => ['mission:read']
        ]);
    }

    #[Route('/{id}/heures', name: 'heures', methods: ['GET'])]
    public function heures(Request $request, Collaborateur $collaborateur): JsonResponse
    {
        $debut = $request->query->get('debut');
        $fin = $request->query->get('fin');

        $semainesTravail = $collaborateur->getSemainesTravail();
        
        if ($debut && $fin) {
            $semainesTravail = $semainesTravail->filter(function($semaine) use ($debut, $fin) {
                return $semaine->getSemaineAnnee() >= $debut && $semaine->getSemaineAnnee() <= $fin;
            });
        }

        $statistiques = $this->heureCalculator->getStatistiquesHeures(
            $collaborateur, 
            $semainesTravail->toArray()
        );

        return $this->json($statistiques);
    }

    #[Route('/forfait-jour', name: 'forfait_jour', methods: ['GET'])]
    public function forfaitJour(): JsonResponse
    {
        $collaborateurs = $this->collaborateurRepository->findForfaitJour();
        
        return $this->json($collaborateurs, Response::HTTP_OK, [], [
            'groups' => ['collaborateur:read']
        ]);
    }

    #[Route('/actifs', name: 'actifs', methods: ['GET'])]
    public function actifs(Request $request): JsonResponse
    {
        $date = $request->query->get('date');
        $dateObj = $date ? new \DateTime($date) : new \DateTime();
        
        $collaborateurs = $this->collaborateurRepository->findWithActiveMissions($dateObj);
        
        return $this->json($collaborateurs, Response::HTTP_OK, [], [
            'groups' => ['collaborateur:read']
        ]);
    }
}

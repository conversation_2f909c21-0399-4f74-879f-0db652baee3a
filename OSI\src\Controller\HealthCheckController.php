<?php

namespace App\Controller;

use App\Repository\CollaborateurRepository;
use App\Repository\MissionRepository;
use App\Repository\SegmentRepository;
use App\Repository\SemaineTravailRepository;
use App\Service\PrimeCalculatorService;
use App\Service\HeureCalculatorService;
use Doctrine\DBAL\Connection;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Routing\Annotation\Route;

class HealthCheckController extends AbstractController
{
    public function __construct(
        private Connection $connection,
        private CollaborateurRepository $collaborateurRepository,
        private MissionRepository $missionRepository,
        private SegmentRepository $segmentRepository,
        private SemaineTravailRepository $semaineTravailRepository,
        private PrimeCalculatorService $primeCalculator,
        private HeureCalculatorService $heureCalculator
    ) {}

    #[Route('/health-check', name: 'health_check')]
    public function healthCheck(): JsonResponse
    {
        $checks = [];
        $overallStatus = 'OK';

        try {
            // 1. Test de connexion à la base de données
            $checks['database'] = $this->checkDatabase();
            
            // 2. Test des entités et repositories
            $checks['entities'] = $this->checkEntities();
            
            // 3. Test des services métiers
            $checks['services'] = $this->checkServices();
            
            // 4. Test de l'API
            $checks['api'] = $this->checkApiEndpoints();
            
            // 5. Vérification des données
            $checks['data'] = $this->checkData();

            // Déterminer le statut global
            foreach ($checks as $check) {
                if ($check['status'] !== 'OK') {
                    $overallStatus = 'WARNING';
                    if ($check['status'] === 'ERROR') {
                        $overallStatus = 'ERROR';
                        break;
                    }
                }
            }

        } catch (\Exception $e) {
            $overallStatus = 'ERROR';
            $checks['global_error'] = [
                'status' => 'ERROR',
                'message' => $e->getMessage()
            ];
        }

        return $this->json([
            'status' => $overallStatus,
            'timestamp' => new \DateTime(),
            'checks' => $checks,
            'summary' => $this->generateSummary($checks)
        ]);
    }

    private function checkDatabase(): array
    {
        try {
            // Test de connexion
            $version = $this->connection->fetchOne('SELECT VERSION()');
            $database = $this->connection->getDatabase();
            
            // Test des tables
            $schemaManager = $this->connection->createSchemaManager();
            $tables = $schemaManager->listTableNames();
            
            $expectedTables = ['collaborateur', 'mission', 'segment', 'semaine_travail'];
            $missingTables = array_diff($expectedTables, $tables);
            
            if (!empty($missingTables)) {
                return [
                    'status' => 'ERROR',
                    'message' => 'Tables manquantes: ' . implode(', ', $missingTables),
                    'database' => $database,
                    'version' => $version
                ];
            }
            
            return [
                'status' => 'OK',
                'database' => $database,
                'version' => $version,
                'tables' => count($tables)
            ];
            
        } catch (\Exception $e) {
            return [
                'status' => 'ERROR',
                'message' => $e->getMessage()
            ];
        }
    }

    private function checkEntities(): array
    {
        try {
            $collaborateurs = $this->collaborateurRepository->findAll();
            $missions = $this->missionRepository->findAll();
            $segments = $this->segmentRepository->findAll();
            $semaines = $this->semaineTravailRepository->findAll();
            
            return [
                'status' => 'OK',
                'counts' => [
                    'collaborateurs' => count($collaborateurs),
                    'missions' => count($missions),
                    'segments' => count($segments),
                    'semaines_travail' => count($semaines)
                ]
            ];
            
        } catch (\Exception $e) {
            return [
                'status' => 'ERROR',
                'message' => $e->getMessage()
            ];
        }
    }

    private function checkServices(): array
    {
        try {
            $results = [];
            
            // Test du service de calcul des primes
            $missions = $this->missionRepository->findAll();
            if (!empty($missions)) {
                $mission = $missions[0];
                $prime = $this->primeCalculator->calculPrimesTotalesMission($mission);
                $results['primes'] = ['calculated' => true, 'sample_prime' => $prime];
            }
            
            // Test du service de calcul des heures
            $semaines = $this->semaineTravailRepository->findAll();
            if (!empty($semaines)) {
                $semaine = $semaines[0];
                $calcul = $this->heureCalculator->calculHeuresSemaine($semaine);
                $results['heures'] = ['calculated' => true, 'sample_calcul' => $calcul];
            }
            
            return [
                'status' => 'OK',
                'services' => $results
            ];
            
        } catch (\Exception $e) {
            return [
                'status' => 'ERROR',
                'message' => $e->getMessage()
            ];
        }
    }

    private function checkApiEndpoints(): array
    {
        try {
            // Vérifier que les routes API existent
            $router = $this->container->get('router');
            $routes = $router->getRouteCollection();
            
            $apiRoutes = [];
            foreach ($routes as $name => $route) {
                if (str_starts_with($name, 'api_')) {
                    $apiRoutes[] = $name;
                }
            }
            
            return [
                'status' => 'OK',
                'api_routes_count' => count($apiRoutes),
                'sample_routes' => array_slice($apiRoutes, 0, 5)
            ];
            
        } catch (\Exception $e) {
            return [
                'status' => 'ERROR',
                'message' => $e->getMessage()
            ];
        }
    }

    private function checkData(): array
    {
        try {
            $issues = [];
            
            // Vérifier l'intégrité des données
            $collaborateurs = $this->collaborateurRepository->findAll();
            $missions = $this->missionRepository->findAll();
            
            // Vérifier que chaque mission a un collaborateur
            foreach ($missions as $mission) {
                if (!$mission->getCollaborateur()) {
                    $issues[] = "Mission {$mission->getId()} sans collaborateur";
                }
            }
            
            // Vérifier les segments orphelins
            $segments = $this->segmentRepository->findAll();
            foreach ($segments as $segment) {
                if (!$segment->getMission()) {
                    $issues[] = "Segment {$segment->getId()} sans mission";
                }
            }
            
            $status = empty($issues) ? 'OK' : 'WARNING';
            
            return [
                'status' => $status,
                'issues' => $issues,
                'data_integrity' => empty($issues) ? 'Good' : 'Issues found'
            ];
            
        } catch (\Exception $e) {
            return [
                'status' => 'ERROR',
                'message' => $e->getMessage()
            ];
        }
    }

    private function generateSummary(array $checks): array
    {
        $summary = [
            'total_checks' => count($checks),
            'passed' => 0,
            'warnings' => 0,
            'errors' => 0
        ];
        
        foreach ($checks as $check) {
            switch ($check['status']) {
                case 'OK':
                    $summary['passed']++;
                    break;
                case 'WARNING':
                    $summary['warnings']++;
                    break;
                case 'ERROR':
                    $summary['errors']++;
                    break;
            }
        }
        
        return $summary;
    }
}

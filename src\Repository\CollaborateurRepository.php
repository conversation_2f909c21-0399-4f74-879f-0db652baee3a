<?php

namespace App\Repository;

use App\Entity\Collaborateur;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<Collaborateur>
 */
class CollaborateurRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Collaborateur::class);
    }

    public function save(Collaborateur $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(Collaborateur $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    /**
     * Trouve les collaborateurs par rôle
     */
    public function findByRole(string $role): array
    {
        return $this->createQueryBuilder('c')
            ->andWhere('c.role = :role')
            ->setParameter('role', $role)
            ->orderBy('c.nom', 'ASC')
            ->addOrderBy('c.prenom', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Trouve les collaborateurs en forfait jour
     */
    public function findForfaitJour(): array
    {
        return $this->createQueryBuilder('c')
            ->andWhere('c.forfaitJour = :forfaitJour')
            ->setParameter('forfaitJour', true)
            ->orderBy('c.nom', 'ASC')
            ->addOrderBy('c.prenom', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Trouve les collaborateurs avec leurs missions actives
     */
    public function findWithActiveMissions(\DateTimeInterface $date = null): array
    {
        if ($date === null) {
            $date = new \DateTime();
        }

        return $this->createQueryBuilder('c')
            ->leftJoin('c.missions', 'm')
            ->andWhere('m.dateDebut <= :date AND m.dateFin >= :date')
            ->setParameter('date', $date)
            ->orderBy('c.nom', 'ASC')
            ->addOrderBy('c.prenom', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Recherche par nom ou prénom
     */
    public function findByNomOrPrenom(string $search): array
    {
        return $this->createQueryBuilder('c')
            ->andWhere('c.nom LIKE :search OR c.prenom LIKE :search')
            ->setParameter('search', '%' . $search . '%')
            ->orderBy('c.nom', 'ASC')
            ->addOrderBy('c.prenom', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Statistiques des collaborateurs
     */
    public function getStatistiques(): array
    {
        $qb = $this->createQueryBuilder('c');
        
        return [
            'total' => $qb->select('COUNT(c.id)')->getQuery()->getSingleScalarResult(),
            'forfaitJour' => $qb->select('COUNT(c.id)')
                ->andWhere('c.forfaitJour = :forfaitJour')
                ->setParameter('forfaitJour', true)
                ->getQuery()
                ->getSingleScalarResult(),
            'moyenneHoraireHebdo' => $qb->select('AVG(c.horaireHebdo)')
                ->getQuery()
                ->getSingleScalarResult()
        ];
    }
}

{% extends 'base.html.twig' %}

{% block title %}Utilisateurs{% endblock %}

{% block body %}
<div class="container mx-auto px-4 py-8">
    <div class="sm:flex sm:items-center">
        <div class="sm:flex-auto">
            <h1 class="text-2xl font-semibold text-gray-900">Utilisateurs</h1>
            <p class="mt-2 text-sm text-gray-700">Liste de tous les utilisateurs actifs</p>
        </div>
        <div class="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
            <button type="button" onclick="openAddUserModal()" class="inline-flex items-center justify-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700">
                Ajouter un utilisateur
            </button>
        </div>
    </div>

    <div class="mt-8 flex flex-col">
        <div class="-my-2 -mx-4 overflow-x-auto sm:-mx-6 lg:-mx-8">
            <div class="inline-block min-w-full py-2 align-middle md:px-6 lg:px-8">
                <div class="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
                    <table class="min-w-full divide-y divide-gray-300">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Nom</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rôle</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Horaire</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Statut</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            {% for user in users %}
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <div class="flex-shrink-0 h-10 w-10">
                                                <div class="h-10 w-10 rounded-full bg-blue-500 flex items-center justify-center">
                                                    <span class="text-white font-medium">{{ user.prenom|first }}{{ user.nom|first }}</span>
                                                </div>
                                            </div>
                                            <div class="ml-4">
                                                <div class="text-sm font-medium text-gray-900">{{ user.nomComplet }}</div>
                                                <div class="text-sm text-gray-500">{{ user.telephone ?? 'N/A' }}</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ user.email }}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ user.role }}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        {{ user.horaireHebdo }}h/semaine
                                        {% if user.forfaitJour %}
                                            <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                                Forfait jour
                                            </span>
                                        {% endif %}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        {% if user.actif %}
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                Actif
                                            </span>
                                        {% else %}
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                                Inactif
                                            </span>
                                        {% endif %}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <div class="flex space-x-2">
                                            <a href="{{ path('app_user_detail', {id: user.id}) }}" class="text-blue-600 hover:text-blue-900">Voir</a>
                                            <button onclick="editUser({{ user.id }})" class="text-indigo-600 hover:text-indigo-900">Modifier</button>
                                            <button onclick="deleteUser({{ user.id }}, '{{ user.nomComplet }}')" class="text-red-600 hover:text-red-900">Supprimer</button>
                                        </div>
                                    </td>
                                </tr>
                            {% else %}
                                <tr>
                                    <td colspan="6" class="px-6 py-4 text-center text-gray-500">Aucun utilisateur trouvé</td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal d'ajout d'utilisateur -->
<div id="addUserModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">Ajouter un utilisateur</h3>
                <button type="button" onclick="closeAddUserModal()" class="text-gray-400 hover:text-gray-600">
                    <span class="sr-only">Fermer</span>
                    <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>

            <form id="addUserForm">
                <div class="grid grid-cols-1 gap-4">
                    <div>
                        <label for="nom" class="block text-sm font-medium text-gray-700">Nom *</label>
                        <input type="text" id="nom" name="nom" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                    </div>

                    <div>
                        <label for="prenom" class="block text-sm font-medium text-gray-700">Prénom *</label>
                        <input type="text" id="prenom" name="prenom" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                    </div>

                    <div>
                        <label for="email" class="block text-sm font-medium text-gray-700">Email *</label>
                        <input type="email" id="email" name="email" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                    </div>

                    <div>
                        <label for="role" class="block text-sm font-medium text-gray-700">Rôle *</label>
                        <select id="role" name="role" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                            <option value="">Sélectionner un rôle</option>
                            <option value="Consultant">Consultant</option>
                            <option value="Senior Consultant">Senior Consultant</option>
                            <option value="Manager">Manager</option>
                            <option value="Senior Manager">Senior Manager</option>
                            <option value="Director">Director</option>
                            <option value="Partner">Partner</option>
                        </select>
                    </div>

                    <div>
                        <label for="telephone" class="block text-sm font-medium text-gray-700">Téléphone</label>
                        <input type="tel" id="telephone" name="telephone" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                    </div>

                    <div>
                        <label for="horaireHebdo" class="block text-sm font-medium text-gray-700">Horaire hebdomadaire *</label>
                        <input type="number" id="horaireHebdo" name="horaireHebdo" value="35" min="1" max="60" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                    </div>

                    <div>
                        <label for="dateEmbauche" class="block text-sm font-medium text-gray-700">Date d'embauche</label>
                        <input type="date" id="dateEmbauche" name="dateEmbauche" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                    </div>

                    <div class="flex items-center">
                        <input type="checkbox" id="forfaitJour" name="forfaitJour" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                        <label for="forfaitJour" class="ml-2 block text-sm text-gray-900">Forfait jour</label>
                    </div>
                </div>

                <div class="mt-6 flex justify-end space-x-3">
                    <button type="button" onclick="closeAddUserModal()" class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50">
                        Annuler
                    </button>
                    <button type="submit" class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700">
                        Créer
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal d'édition d'utilisateur -->
<div id="editUserModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">Modifier l'utilisateur</h3>
                <button type="button" onclick="closeEditUserModal()" class="text-gray-400 hover:text-gray-600">
                    <span class="sr-only">Fermer</span>
                    <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>

            <form id="editUserForm">
                <input type="hidden" id="editUserId" name="userId">

                <div class="grid grid-cols-1 gap-4">
                    <div>
                        <label for="editNom" class="block text-sm font-medium text-gray-700">Nom *</label>
                        <input type="text" id="editNom" name="nom" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                    </div>

                    <div>
                        <label for="editPrenom" class="block text-sm font-medium text-gray-700">Prénom *</label>
                        <input type="text" id="editPrenom" name="prenom" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                    </div>

                    <div>
                        <label for="editEmail" class="block text-sm font-medium text-gray-700">Email *</label>
                        <input type="email" id="editEmail" name="email" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                    </div>

                    <div>
                        <label for="editRole" class="block text-sm font-medium text-gray-700">Rôle *</label>
                        <select id="editRole" name="role" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                            <option value="">Sélectionner un rôle</option>
                            <option value="Consultant">Consultant</option>
                            <option value="Senior Consultant">Senior Consultant</option>
                            <option value="Manager">Manager</option>
                            <option value="Senior Manager">Senior Manager</option>
                            <option value="Director">Director</option>
                            <option value="Partner">Partner</option>
                        </select>
                    </div>

                    <div>
                        <label for="editTelephone" class="block text-sm font-medium text-gray-700">Téléphone</label>
                        <input type="tel" id="editTelephone" name="telephone" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                    </div>

                    <div>
                        <label for="editHoraireHebdo" class="block text-sm font-medium text-gray-700">Horaire hebdomadaire *</label>
                        <input type="number" id="editHoraireHebdo" name="horaireHebdo" min="1" max="60" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                    </div>

                    <div>
                        <label for="editDateEmbauche" class="block text-sm font-medium text-gray-700">Date d'embauche</label>
                        <input type="date" id="editDateEmbauche" name="dateEmbauche" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                    </div>

                    <div>
                        <label for="editDateDepart" class="block text-sm font-medium text-gray-700">Date de départ</label>
                        <input type="date" id="editDateDepart" name="dateDepart" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                    </div>

                    <div class="flex items-center">
                        <input type="checkbox" id="editForfaitJour" name="forfaitJour" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                        <label for="editForfaitJour" class="ml-2 block text-sm text-gray-900">Forfait jour</label>
                    </div>

                    <div class="flex items-center">
                        <input type="checkbox" id="editActif" name="actif" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                        <label for="editActif" class="ml-2 block text-sm text-gray-900">Actif</label>
                    </div>
                </div>

                <div class="mt-6 flex justify-end space-x-3">
                    <button type="button" onclick="closeEditUserModal()" class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50">
                        Annuler
                    </button>
                    <button type="submit" class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700">
                        Modifier
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block javascripts %}
{{ parent() }}
<script>
// Gestion du modal
function openAddUserModal() {
    document.getElementById('addUserModal').classList.remove('hidden');
}

function closeAddUserModal() {
    document.getElementById('addUserModal').classList.add('hidden');
    document.getElementById('addUserForm').reset();
}

// Fermer le modal en cliquant à l'extérieur
document.getElementById('addUserModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeAddUserModal();
    }
});

// Gestion du formulaire d'ajout
document.getElementById('addUserForm').addEventListener('submit', async function(e) {
    e.preventDefault();

    const formData = new FormData(e.target);
    const data = {
        nom: formData.get('nom'),
        prenom: formData.get('prenom'),
        email: formData.get('email'),
        role: formData.get('role'),
        telephone: formData.get('telephone') || null,
        horaireHebdo: parseInt(formData.get('horaireHebdo')),
        forfaitJour: formData.get('forfaitJour') === 'on',
        dateEmbauche: formData.get('dateEmbauche') || null
    };

    try {
        const response = await ajax.post("{{ path('api_user_create') }}", data);

        if (response.status === 201) {
            closeAddUserModal();
            location.reload(); // Recharger la page pour voir le nouvel utilisateur
        }
    } catch (error) {
        console.error('Erreur lors de la création de l\'utilisateur:', error);
        alert('Erreur lors de la création de l\'utilisateur. Vérifiez les données saisies.');
    }
});

// Gestion du modal d'édition
function closeEditUserModal() {
    document.getElementById('editUserModal').classList.add('hidden');
    document.getElementById('editUserForm').reset();
}

// Fermer le modal d'édition en cliquant à l'extérieur
document.getElementById('editUserModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeEditUserModal();
    }
});

// Fonction pour éditer un utilisateur
async function editUser(userId) {
    try {
        // Charger les données de l'utilisateur
        const response = await ajax.get(`{{ path('api_user_show', {id: '__ID__'}) }}`.replace('__ID__', userId));
        const user = response.data || response;

        // Remplir le formulaire
        document.getElementById('editUserId').value = user.id;
        document.getElementById('editNom').value = user.nom || '';
        document.getElementById('editPrenom').value = user.prenom || '';
        document.getElementById('editEmail').value = user.email || '';
        document.getElementById('editRole').value = user.role || '';
        document.getElementById('editTelephone').value = user.telephone || '';
        document.getElementById('editHoraireHebdo').value = user.horaireHebdo || 35;
        document.getElementById('editDateEmbauche').value = user.dateEmbauche ? user.dateEmbauche.split('T')[0] : '';
        document.getElementById('editDateDepart').value = user.dateDepart ? user.dateDepart.split('T')[0] : '';
        document.getElementById('editForfaitJour').checked = user.forfaitJour || false;
        document.getElementById('editActif').checked = user.actif !== false;

        // Ouvrir le modal
        document.getElementById('editUserModal').classList.remove('hidden');
    } catch (error) {
        console.error('Erreur lors du chargement de l\'utilisateur:', error);
        alert('Erreur lors du chargement des données de l\'utilisateur');
    }
}

// Gestion du formulaire d'édition
document.getElementById('editUserForm').addEventListener('submit', async function(e) {
    e.preventDefault();

    const formData = new FormData(e.target);
    const userId = formData.get('userId');
    const data = {
        nom: formData.get('nom'),
        prenom: formData.get('prenom'),
        email: formData.get('email'),
        role: formData.get('role'),
        telephone: formData.get('telephone') || null,
        horaireHebdo: parseInt(formData.get('horaireHebdo')),
        forfaitJour: formData.get('forfaitJour') === 'on',
        actif: formData.get('actif') === 'on',
        dateEmbauche: formData.get('dateEmbauche') || null,
        dateDepart: formData.get('dateDepart') || null
    };

    try {
        const response = await ajax.put(`{{ path('api_user_update', {id: '__ID__'}) }}`.replace('__ID__', userId), data);

        if (response.status === 200) {
            closeEditUserModal();
            location.reload();
        }
    } catch (error) {
        console.error('Erreur lors de la modification de l\'utilisateur:', error);
        alert('Erreur lors de la modification de l\'utilisateur. Vérifiez les données saisies.');
    }
});

// Fonction pour supprimer un utilisateur
async function deleteUser(userId, userName) {
    if (!confirm(`Êtes-vous sûr de vouloir supprimer l'utilisateur "${userName}" ?\n\nCette action est irréversible.`)) {
        return;
    }

    try {
        const response = await ajax.delete(`{{ path('api_user_delete', {id: '__ID__'}) }}`.replace('__ID__', userId));

        if (response.status === 204) {
            location.reload();
        }
    } catch (error) {
        console.error('Erreur lors de la suppression de l\'utilisateur:', error);
        alert('Erreur lors de la suppression de l\'utilisateur. Il est peut-être encore assigné à des missions.');
    }
}
</script>
{% endblock %}
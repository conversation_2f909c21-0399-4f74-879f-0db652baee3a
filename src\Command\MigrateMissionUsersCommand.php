<?php

namespace App\Command;

use App\Entity\Mission;
use App\Entity\User;
use App\Entity\Collaborateur;
use App\Repository\MissionRepository;
use App\Repository\UserRepository;
use App\Repository\CollaborateurRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

#[AsCommand(
    name: 'app:migrate-mission-users',
    description: 'Migre les missions de l\'ancien système (collaborateur) vers le nouveau (users)'
)]
class MigrateMissionUsersCommand extends Command
{
    public function __construct(
        private EntityManagerInterface $entityManager,
        private MissionRepository $missionRepository,
        private UserRepository $userRepository,
        private CollaborateurRepository $collaborateurRepository
    ) {
        parent::__construct();
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);

        $io->title('Migration des missions vers le nouveau système utilisateurs');

        // 1. Récupérer toutes les missions
        $missions = $this->missionRepository->findAll();
        $io->info(sprintf('Trouvé %d missions à traiter', count($missions)));

        $migratedCount = 0;
        $skippedCount = 0;
        $errorCount = 0;

        foreach ($missions as $mission) {
            try {
                $io->text(sprintf('Traitement de la mission: %s', $mission->getTitre()));

                // Si la mission a déjà des users, on passe
                if ($mission->getUsers()->count() > 0) {
                    $io->text('  → Déjà des utilisateurs assignés, ignoré');
                    $skippedCount++;
                    continue;
                }

                // Si la mission a un collaborateur, on cherche l'utilisateur correspondant
                if ($mission->getCollaborateur()) {
                    $collaborateur = $mission->getCollaborateur();
                    $io->text(sprintf('  → Collaborateur: %s', $collaborateur->getNomComplet()));

                    // Chercher l'utilisateur correspondant par email
                    $user = $this->userRepository->findOneBy(['email' => $collaborateur->getEmail()]);
                    
                    if ($user) {
                        $mission->addUser($user);
                        $this->entityManager->persist($mission);
                        $io->text(sprintf('  → Utilisateur trouvé et assigné: %s', $user->getNomComplet()));
                        $migratedCount++;
                    } else {
                        $io->warning(sprintf('  → Aucun utilisateur trouvé pour l\'email: %s', $collaborateur->getEmail()));
                        
                        // Créer un utilisateur basé sur le collaborateur
                        $newUser = new User();
                        $newUser->setNom($collaborateur->getNom())
                               ->setPrenom($collaborateur->getPrenom())
                               ->setEmail($collaborateur->getEmail())
                               ->setRole($collaborateur->getRole())
                               ->setHoraireHebdo($collaborateur->getHoraireHebdo())
                               ->setForfaitJour($collaborateur->isForfaitJour());

                        $this->entityManager->persist($newUser);
                        $mission->addUser($newUser);
                        $this->entityManager->persist($mission);
                        
                        $io->text(sprintf('  → Nouvel utilisateur créé et assigné: %s', $newUser->getNomComplet()));
                        $migratedCount++;
                    }
                } else {
                    $io->text('  → Aucun collaborateur assigné, ignoré');
                    $skippedCount++;
                }

            } catch (\Exception $e) {
                $io->error(sprintf('Erreur lors du traitement de la mission %s: %s', $mission->getTitre(), $e->getMessage()));
                $errorCount++;
            }
        }

        // Sauvegarder les changements
        $this->entityManager->flush();

        $io->success('Migration terminée !');
        $io->table(
            ['Statut', 'Nombre'],
            [
                ['Missions migrées', $migratedCount],
                ['Missions ignorées', $skippedCount],
                ['Erreurs', $errorCount],
                ['Total', count($missions)]
            ]
        );

        return Command::SUCCESS;
    }
}

<?php

namespace App\Controller;

use App\Repository\CollaborateurRepository;
use App\Repository\MissionRepository;
use App\Repository\SegmentRepository;
use App\Repository\SemaineTravailRepository;
use App\Service\PrimeCalculatorService;
use App\Service\HeureCalculatorService;
use App\Service\RecuperationCalculatorService;
use App\Service\ValidationService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Routing\Annotation\Route;

class TestServicesController extends AbstractController
{
    public function __construct(
        private CollaborateurRepository $collaborateurRepository,
        private MissionRepository $missionRepository,
        private SegmentRepository $segmentRepository,
        private SemaineTravailRepository $semaineTravailRepository,
        private PrimeCalculatorService $primeCalculator,
        private HeureCalculatorService $heureCalculator,
        private RecuperationCalculatorService $recuperationCalculator,
        private ValidationService $validationService
    ) {}

    #[Route('/test-services', name: 'test_services')]
    public function testServices(): JsonResponse
    {
        $results = [];

        try {
            // Test 1: Calcul des primes
            $missions = $this->missionRepository->findAll();
            if (!empty($missions)) {
                $mission = $missions[0];
                $primesTotales = $this->primeCalculator->calculPrimesTotalesMission($mission);
                $results['primes'] = [
                    'mission' => $mission->getTitre(),
                    'primesTotales' => $primesTotales,
                    'zone' => $mission->getZone(),
                    'niveau' => $mission->getNiveau()
                ];
            }

            // Test 2: Calcul des heures
            $collaborateurs = $this->collaborateurRepository->findAll();
            if (!empty($collaborateurs)) {
                $collaborateur = $collaborateurs[0];
                $semainesTravail = $this->semaineTravailRepository->findByCollaborateur($collaborateur);
                
                if (!empty($semainesTravail)) {
                    $semaine = $semainesTravail[0];
                    $calculHeures = $this->heureCalculator->calculHeuresSemaine($semaine);
                    $results['heures'] = [
                        'collaborateur' => $collaborateur->getNomComplet(),
                        'semaine' => $semaine->getSemaineAnnee(),
                        'calcul' => $calculHeures
                    ];
                }

                // Statistiques des heures
                $statsHeures = $this->heureCalculator->getStatistiquesHeures($collaborateur, $semainesTravail);
                $results['statistiquesHeures'] = $statsHeures;
            }

            // Test 3: Calcul de récupération
            $segments = $this->segmentRepository->findAll();
            if (!empty($segments) && !empty($collaborateurs)) {
                $collaborateur = $collaborateurs[0];
                $segmentsCollaborateur = $this->segmentRepository->findByCollaborateur($collaborateur);
                
                if (!empty($segmentsCollaborateur)) {
                    $recuperation = $this->recuperationCalculator->calculRecuperation($collaborateur, $segmentsCollaborateur);
                    $results['recuperation'] = [
                        'collaborateur' => $collaborateur->getNomComplet(),
                        'recuperation' => $recuperation
                    ];
                }
            }

            // Test 4: Validation
            if (!empty($missions)) {
                $mission = $missions[0];
                $validation = $this->validationService->validerMission($mission);
                $results['validation'] = [
                    'mission' => $mission->getTitre(),
                    'validation' => $validation
                ];
            }

            if (!empty($segments)) {
                $segment = $segments[0];
                $validationSegment = $this->validationService->validerSegment($segment);
                $results['validationSegment'] = [
                    'segment' => $segment->getType(),
                    'validation' => $validationSegment
                ];
            }

            // Test 5: Barème des primes
            $bareme = $this->primeCalculator->getBareme();
            $results['bareme'] = $bareme;

            // Test 6: Majorations des heures
            $majorations = $this->heureCalculator->getMajorations();
            $results['majorations'] = $majorations;

            $results['success'] = true;
            $results['message'] = 'Tous les services fonctionnent correctement';

        } catch (\Exception $e) {
            $results['success'] = false;
            $results['error'] = $e->getMessage();
            $results['trace'] = $e->getTraceAsString();
        }

        return $this->json($results);
    }

    #[Route('/test-api', name: 'test_api')]
    public function testApi(): JsonResponse
    {
        $results = [];

        try {
            // Test des endpoints API
            $endpoints = [
                'collaborateurs' => '/api/collaborateurs',
                'missions' => '/api/missions',
                'segments' => '/api/segments',
                'semaines-travail' => '/api/semaines-travail'
            ];

            foreach ($endpoints as $name => $endpoint) {
                // Simuler un test d'endpoint
                $results['endpoints'][$name] = [
                    'url' => $endpoint,
                    'status' => 'available'
                ];
            }

            // Test des statistiques
            $statsCollaborateurs = $this->collaborateurRepository->getStatistiques();
            $statsMissions = $this->missionRepository->getStatistiques();
            
            $results['statistiques'] = [
                'collaborateurs' => $statsCollaborateurs,
                'missions' => $statsMissions
            ];

            $results['success'] = true;
            $results['message'] = 'API fonctionnelle';

        } catch (\Exception $e) {
            $results['success'] = false;
            $results['error'] = $e->getMessage();
        }

        return $this->json($results);
    }

    #[Route('/test-calendar-data', name: 'test_calendar_data')]
    public function testCalendarData(): JsonResponse
    {
        try {
            $debut = new \DateTime('2024-01-01');
            $fin = new \DateTime('2024-12-31');
            
            $events = $this->segmentRepository->findForCalendar($debut, $fin);
            
            return $this->json([
                'success' => true,
                'events' => $events,
                'count' => count($events)
            ]);

        } catch (\Exception $e) {
            return $this->json([
                'success' => false,
                'error' => $e->getMessage()
            ]);
        }
    }

    #[Route('/test-primes-calculation', name: 'test_primes_calculation')]
    public function testPrimesCalculation(): JsonResponse
    {
        try {
            $missions = $this->missionRepository->findAll();
            $results = [];

            foreach ($missions as $mission) {
                $primesTotales = $this->primeCalculator->calculPrimesTotalesMission($mission);
                $results[] = [
                    'mission' => $mission->getTitre(),
                    'collaborateur' => $mission->getCollaborateur()->getNomComplet(),
                    'pays' => $mission->getPays(),
                    'zone' => $mission->getZone(),
                    'niveau' => $mission->getNiveau(),
                    'duree' => $mission->getDureeJours(),
                    'primesTotales' => $primesTotales
                ];
            }

            return $this->json([
                'success' => true,
                'missions' => $results,
                'totalMissions' => count($results),
                'totalPrimes' => array_sum(array_column($results, 'primesTotales'))
            ]);

        } catch (\Exception $e) {
            return $this->json([
                'success' => false,
                'error' => $e->getMessage()
            ]);
        }
    }
}

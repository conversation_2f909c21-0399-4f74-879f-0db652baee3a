<?php

namespace App\Controller\Api;

use App\Entity\User;
use App\Repository\UserRepository;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

#[Route('/api/users', name: 'api_user_')]
class UserController extends AbstractController
{
    public function __construct(
        private UserRepository $userRepository
    ) {}

    #[Route('', name: 'index', methods: ['GET'])]
    public function index(Request $request): JsonResponse
    {
        $search = $request->query->get('search');
        $role = $request->query->get('role');

        if ($search) {
            $users = $this->userRepository->findByNomPrenom($search);
        } elseif ($role) {
            $users = $this->userRepository->findByRole($role);
        } else {
            $users = $this->userRepository->findActifs();
        }

        return $this->json([
            'data' => $users,
            'total' => count($users)
        ], Response::HTTP_OK, [], [
            'groups' => ['user:read']
        ]);
    }

    #[Route('/{id}', name: 'show', methods: ['GET'])]
    public function show(User $user): JsonResponse
    {
        return $this->json($user, Response::HTTP_OK, [], [
            'groups' => ['user:read', 'user:detail']
        ]);
    }

    #[Route('', name: 'create', methods: ['POST'])]
    public function create(Request $request): JsonResponse
    {
        $data = json_decode($request->getContent(), true);

        $user = new User();
        $user->setNom($data['nom'] ?? '')
            ->setPrenom($data['prenom'] ?? '')
            ->setEmail($data['email'] ?? '')
            ->setRole($data['role'] ?? 'Consultant')
            ->setHoraireHebdo($data['horaireHebdo'] ?? 35)
            ->setForfaitJour($data['forfaitJour'] ?? false)
            ->setTelephone($data['telephone'] ?? null)
            ->setActif(true);

        if (isset($data['dateEmbauche'])) {
            $user->setDateEmbauche(new \DateTime($data['dateEmbauche']));
        }

        $this->userRepository->save($user, true);

        return $this->json($user, Response::HTTP_CREATED, [], [
            'groups' => ['user:read']
        ]);
    }

    #[Route('/{id}', name: 'update', methods: ['PUT', 'PATCH'])]
    public function update(Request $request, User $user): JsonResponse
    {
        $data = json_decode($request->getContent(), true);

        if (isset($data['nom'])) $user->setNom($data['nom']);
        if (isset($data['prenom'])) $user->setPrenom($data['prenom']);
        if (isset($data['email'])) $user->setEmail($data['email']);
        if (isset($data['role'])) $user->setRole($data['role']);
        if (isset($data['horaireHebdo'])) $user->setHoraireHebdo($data['horaireHebdo']);
        if (isset($data['forfaitJour'])) $user->setForfaitJour($data['forfaitJour']);
        if (isset($data['telephone'])) $user->setTelephone($data['telephone']);
        if (isset($data['actif'])) $user->setActif($data['actif']);

        if (isset($data['dateEmbauche'])) {
            $user->setDateEmbauche(new \DateTime($data['dateEmbauche']));
        }

        if (isset($data['dateDepart'])) {
            $user->setDateDepart(new \DateTime($data['dateDepart']));
        }

        $this->userRepository->save($user, true);

        return $this->json($user, Response::HTTP_OK, [], [
            'groups' => ['user:read']
        ]);
    }

    #[Route('/{id}', name: 'delete', methods: ['DELETE'])]
    public function delete(User $user): JsonResponse
    {
        $this->userRepository->remove($user, true);

        return $this->json(null, Response::HTTP_NO_CONTENT);
    }
}

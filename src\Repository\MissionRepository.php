<?php

namespace App\Repository;

use App\Entity\Mission;
use App\Entity\Collaborateur;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<Mission>
 */
class MissionRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Mission::class);
    }

    public function save(Mission $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(Mission $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    /**
     * Trouve les missions actives à une date donnée
     */
    public function findActivesAuDate(\DateTimeInterface $date): array
    {
        return $this->createQueryBuilder('m')
            ->andWhere('m.dateDebut <= :date AND m.dateFin >= :date')
            ->setParameter('date', $date)
            ->orderBy('m.dateDebut', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Trouve les missions d'un collaborateur
     */
    public function findByCollaborateur(Collaborateur $collaborateur): array
    {
        return $this->createQueryBuilder('m')
            ->andWhere('m.collaborateur = :collaborateur')
            ->setParameter('collaborateur', $collaborateur)
            ->orderBy('m.dateDebut', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Trouve les missions par zone
     */
    public function findByZone(string $zone): array
    {
        return $this->createQueryBuilder('m')
            ->andWhere('m.zone = :zone')
            ->setParameter('zone', $zone)
            ->orderBy('m.dateDebut', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Trouve les missions par pays
     */
    public function findByPays(string $pays): array
    {
        return $this->createQueryBuilder('m')
            ->andWhere('m.pays = :pays')
            ->setParameter('pays', $pays)
            ->orderBy('m.dateDebut', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Trouve les missions dans une période
     */
    public function findInPeriod(\DateTimeInterface $debut, \DateTimeInterface $fin): array
    {
        return $this->createQueryBuilder('m')
            ->andWhere('(m.dateDebut BETWEEN :debut AND :fin) OR (m.dateFin BETWEEN :debut AND :fin) OR (m.dateDebut <= :debut AND m.dateFin >= :fin)')
            ->setParameter('debut', $debut)
            ->setParameter('fin', $fin)
            ->orderBy('m.dateDebut', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Trouve les missions avec leurs segments
     */
    public function findWithSegments(): array
    {
        return $this->createQueryBuilder('m')
            ->leftJoin('m.segments', 's')
            ->addSelect('s')
            ->orderBy('m.dateDebut', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Statistiques des missions
     */
    public function getStatistiques(): array
    {
        $qb = $this->createQueryBuilder('m');
        
        $total = $qb->select('COUNT(m.id)')->getQuery()->getSingleScalarResult();
        
        $parZone = $this->createQueryBuilder('m')
            ->select('m.zone, COUNT(m.id) as count')
            ->groupBy('m.zone')
            ->getQuery()
            ->getResult();

        $parNiveau = $this->createQueryBuilder('m')
            ->select('m.niveau, COUNT(m.id) as count')
            ->groupBy('m.niveau')
            ->getQuery()
            ->getResult();

        return [
            'total' => $total,
            'parZone' => $parZone,
            'parNiveau' => $parNiveau
        ];
    }

    /**
     * Trouve les missions en cours
     */
    public function findEnCours(): array
    {
        $today = new \DateTime();
        return $this->findActivesAuDate($today);
    }

    /**
     * Trouve les prochaines missions
     */
    public function findProchaines(int $limit = 10): array
    {
        $today = new \DateTime();
        
        return $this->createQueryBuilder('m')
            ->andWhere('m.dateDebut > :today')
            ->setParameter('today', $today)
            ->orderBy('m.dateDebut', 'ASC')
            ->setMaxResults($limit)
            ->getQuery()
            ->getResult();
    }
}
